"""
Simple Phase 2 Implementation Test for GretahAI ScriptWeaver

This test validates the core Phase 2 browser session continuity components
without requiring complex browser automation or external dependencies.
"""

import sys
import os

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_session_aware_script_generator():
    """Test the session-aware script generator import and basic functionality."""
    print("Test 1: Session-Aware Script Generator")
    print("-" * 40)
    
    try:
        from core.session_aware_script_generator import SessionAwareScriptGenerator
        print("✓ SessionAwareScriptGenerator imported successfully")
        
        # Test basic instantiation
        generator = SessionAwareScriptGenerator()
        print("✓ SessionAwareScriptGenerator instantiated successfully")
        
        # Test basic script enhancement
        basic_script = '''
def test_example(browser):
    browser.get("https://example.com")
    assert "Example" in browser.title
'''
        
        test_case = {
            "Test Case ID": "TC_001",
            "Test Case Name": "Test Case"
        }
        
        enhanced_script, metadata = generator.generate_session_aware_script(
            basic_script, test_case, enable_continuity=True
        )
        
        if metadata.get("session_aware"):
            print("✓ Session-aware script generated successfully")
            return True
        else:
            print("✗ Session awareness not enabled in generated script")
            return False
            
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False

def test_enhanced_state_manager():
    """Test the enhanced StateManager with Phase 2 features."""
    print("\nTest 2: Enhanced StateManager")
    print("-" * 40)
    
    try:
        from state_manager import StateManager
        print("✓ StateManager imported successfully")
        
        # Test instantiation with Phase 2 features
        state = StateManager()
        print("✓ StateManager instantiated successfully")
        
        # Test Phase 2 attributes
        phase2_attributes = [
            'session_persistence_active',
            'session_state_preserved',
            'session_cycle_count',
            'session_navigation_history',
            'session_error_recovery_count'
        ]
        
        missing_attributes = []
        for attr in phase2_attributes:
            if not hasattr(state, attr):
                missing_attributes.append(attr)
        
        if missing_attributes:
            print(f"✗ Missing Phase 2 attributes: {missing_attributes}")
            return False
        else:
            print("✓ All Phase 2 attributes present")
        
        # Test Phase 2 methods
        phase2_methods = [
            'start_session_persistence_cycle',
            'update_session_state_after_execution',
            'validate_session_continuity',
            'reset_session_continuity_state'
        ]
        
        missing_methods = []
        for method in phase2_methods:
            if not hasattr(state, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"✗ Missing Phase 2 methods: {missing_methods}")
            return False
        else:
            print("✓ All Phase 2 methods present")
        
        # Test basic functionality
        state.start_session_persistence_cycle("https://example.com")
        if state.session_persistence_active:
            print("✓ Session persistence cycle started successfully")
        else:
            print("✗ Session persistence cycle not started")
            return False
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False

def test_workflow_integration():
    """Test the workflow integration enhancements."""
    print("\nTest 3: Workflow Integration")
    print("-" * 40)
    
    try:
        from core.workflow_integration import WorkflowBrowserIntegration
        print("✓ WorkflowBrowserIntegration imported successfully")
        
        # Test instantiation
        integration = WorkflowBrowserIntegration()
        print("✓ WorkflowBrowserIntegration instantiated successfully")
        
        # Test Phase 2 method
        if hasattr(integration, 'complete_workflow_cycle'):
            print("✓ complete_workflow_cycle method present")
        else:
            print("✗ complete_workflow_cycle method missing")
            return False
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False

def test_ui_components():
    """Test the enhanced UI components."""
    print("\nTest 4: Enhanced UI Components")
    print("-" * 40)
    
    try:
        from ui_components.browser_continuity_components import render_enhanced_continuity_workflow_indicator
        print("✓ Enhanced workflow indicator imported successfully")
        
        # Test that the function is callable
        if callable(render_enhanced_continuity_workflow_indicator):
            print("✓ Enhanced workflow indicator is callable")
        else:
            print("✗ Enhanced workflow indicator is not callable")
            return False
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False

def test_ai_generation_enhancement():
    """Test the AI generation enhancement."""
    print("\nTest 5: AI Generation Enhancement")
    print("-" * 40)
    
    try:
        from core.ai_generation import generate_test_script
        print("✓ generate_test_script imported successfully")
        
        # Check if the function signature includes enable_session_continuity parameter
        import inspect
        sig = inspect.signature(generate_test_script)
        
        if 'enable_session_continuity' in sig.parameters:
            print("✓ enable_session_continuity parameter present")
        else:
            print("✗ enable_session_continuity parameter missing")
            return False
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False

def main():
    """Run all Phase 2 simple tests."""
    print("Phase 2 Simple Implementation Tests for GretahAI ScriptWeaver")
    print("=" * 60)
    
    tests = [
        test_session_aware_script_generator,
        test_enhanced_state_manager,
        test_workflow_integration,
        test_ui_components,
        test_ai_generation_enhancement
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append(False)
    
    # Print summary
    print("\n" + "=" * 60)
    print("Phase 2 Simple Test Results:")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    test_names = [
        "Session-Aware Script Generator",
        "Enhanced StateManager",
        "Workflow Integration",
        "Enhanced UI Components",
        "AI Generation Enhancement"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "PASS" if result else "FAIL"
        print(f"{name:<35} {status}")
    
    print("=" * 60)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n✓ SUCCESS: Phase 2 core implementation is functional!")
        print("✓ All core components are properly integrated")
        print("✓ Session-aware script generation is working")
        print("✓ Enhanced StateManager is operational")
        print("✓ Workflow integration enhancements are ready")
        print("✓ UI components are properly enhanced")
        print("✓ AI generation pipeline is enhanced")
        print("\nPhase 2 browser session continuity core implementation is ready!")
        return True
    else:
        print(f"\n✗ FAILURE: {total - passed} tests failed.")
        print("Phase 2 implementation needs additional work.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

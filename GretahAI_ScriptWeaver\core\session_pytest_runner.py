"""
Session-Aware Pytest Runner for GretahAI ScriptWeaver

This module provides pytest execution capabilities while preserving existing
browser sessions, enabling true state continuity across workflow stages.

Key Features:
- Execute pytest scripts with existing WebDriver instances
- Preserve browser state (DOM, cookies, session storage) during execution
- Session injection and management for generated test scripts
- Fallback to standard execution when session continuity fails
- Comprehensive error handling and recovery mechanisms
"""

import os
import sys
import subprocess
import tempfile
import json
import logging
from typing import Tuple, Dict, Any, Optional
from selenium import webdriver
from selenium.webdriver.chrome.webdriver import WebDriver
from datetime import datetime

logger = logging.getLogger("ScriptWeaver.session_pytest_runner")

class SessionPytestRunner:
    """
    Executes pytest scripts while preserving existing browser sessions.
    
    This class manages the execution of generated test scripts using an existing
    WebDriver instance, maintaining browser state continuity throughout the process.
    """
    
    def __init__(self, driver: WebDriver, session_id: str):
        """
        Initialize the session pytest runner.
        
        Args:
            driver: Existing WebDriver instance to preserve
            session_id: Session ID for tracking and validation
        """
        self.driver = driver
        self.session_id = session_id
        self.session_data_file = None
        self.modified_script_path = None
        
    def execute_script(self, script_path: str) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Execute a pytest script with session preservation.
        
        Args:
            script_path: Path to the script file to execute
            
        Returns:
            Tuple[bool, str, Dict]: (success, message, execution_results)
        """
        try:
            # Validate inputs
            if not os.path.exists(script_path):
                return False, f"Script file not found: {script_path}", {}
            
            if not self._validate_driver():
                return False, "Browser session is not valid or responsive", {}
            
            # Prepare session data for injection
            session_data = self._prepare_session_data()
            
            # Create session data file
            self.session_data_file = self._create_session_data_file(session_data)
            
            # Modify script for session continuity
            self.modified_script_path = self._prepare_script_for_session(script_path)
            
            # Execute pytest with session preservation
            success, message, results = self._execute_pytest_with_session()
            
            return success, message, results
            
        except Exception as e:
            logger.error(f"Error in session pytest execution: {e}")
            return False, f"Session execution failed: {e}", {}
        finally:
            # Cleanup temporary files
            self._cleanup_temp_files()
    
    def _validate_driver(self) -> bool:
        """Validate that the WebDriver instance is still active and responsive."""
        try:
            # Test basic responsiveness
            current_url = self.driver.current_url
            ready_state = self.driver.execute_script("return document.readyState;")
            
            logger.debug(f"Driver validation - URL: {current_url}, Ready state: {ready_state}")
            return True
            
        except Exception as e:
            logger.error(f"Driver validation failed: {e}")
            return False
    
    def _prepare_session_data(self) -> Dict[str, Any]:
        """
        Prepare session data for injection into the test script.
        
        Returns:
            Dict containing session data for script injection
        """
        try:
            session_data = {
                "session_id": self.session_id,
                "current_url": self.driver.current_url,
                "window_handle": self.driver.current_window_handle,
                "cookies": self.driver.get_cookies(),
                "local_storage": {},
                "session_storage": {},
                "page_title": self.driver.title,
                "timestamp": datetime.now().isoformat()
            }
            
            # Get local storage data
            try:
                local_storage_script = """
                var storage = {};
                for (var i = 0; i < localStorage.length; i++) {
                    var key = localStorage.key(i);
                    storage[key] = localStorage.getItem(key);
                }
                return storage;
                """
                session_data["local_storage"] = self.driver.execute_script(local_storage_script)
            except Exception as e:
                logger.warning(f"Could not retrieve local storage: {e}")
            
            # Get session storage data
            try:
                session_storage_script = """
                var storage = {};
                for (var i = 0; i < sessionStorage.length; i++) {
                    var key = sessionStorage.key(i);
                    storage[key] = sessionStorage.getItem(key);
                }
                return storage;
                """
                session_data["session_storage"] = self.driver.execute_script(session_storage_script)
            except Exception as e:
                logger.warning(f"Could not retrieve session storage: {e}")
            
            logger.info(f"Prepared session data with {len(session_data['cookies'])} cookies")
            return session_data
            
        except Exception as e:
            logger.error(f"Error preparing session data: {e}")
            return {
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    def _create_session_data_file(self, session_data: Dict[str, Any]) -> str:
        """
        Create a temporary file containing session data for script injection.
        
        Args:
            session_data: Session data to write to file
            
        Returns:
            str: Path to the created session data file
        """
        try:
            # Create temporary file for session data
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(session_data, f, indent=2)
                session_file_path = f.name
            
            logger.debug(f"Created session data file: {session_file_path}")
            return session_file_path
            
        except Exception as e:
            logger.error(f"Error creating session data file: {e}")
            raise
    
    def _prepare_script_for_session(self, original_script_path: str) -> str:
        """
        Prepare the test script for session continuity by injecting session support.
        
        Args:
            original_script_path: Path to the original test script
            
        Returns:
            str: Path to the modified script with session support
        """
        try:
            # Read original script
            with open(original_script_path, 'r') as f:
                original_content = f.read()
            
            # Create session-aware script content
            session_aware_content = self._inject_session_support(original_content)
            
            # Create temporary modified script file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(session_aware_content)
                modified_script_path = f.name
            
            logger.debug(f"Created session-aware script: {modified_script_path}")
            return modified_script_path
            
        except Exception as e:
            logger.error(f"Error preparing script for session: {e}")
            raise
    
    def _inject_session_support(self, original_content: str) -> str:
        """
        Inject session support code into the original test script.

        Args:
            original_content: Original script content

        Returns:
            str: Modified script content with session support
        """
        # Create a direct session injection approach that avoids file path issues
        # by embedding the session data directly in the script

        # Get session data as a JSON string
        session_data = self._prepare_session_data()
        session_data_json = json.dumps(session_data, indent=2)

        session_injection_code = f'''
# =========================================================================
# SESSION CONTINUITY INJECTION - AUTO-GENERATED
# =========================================================================
# This code enables the test script to reuse an existing browser session
# from GretahAI ScriptWeaver workflow, preserving state continuity.
# =========================================================================

import os
import json
import pytest
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

# Session data embedded directly (avoids file path issues)
SESSION_DATA = {session_data_json}

# Global variable to store the shared driver instance
_shared_driver = None

def get_shared_driver():
    """Get the shared driver instance for session continuity."""
    global _shared_driver
    return _shared_driver

def set_shared_driver(driver):
    """Set the shared driver instance for session continuity."""
    global _shared_driver
    _shared_driver = driver

@pytest.fixture(scope="module")
def browser_with_session():
    """
    Browser fixture that reuses existing session when available.
    Falls back to creating new browser if session is not available.
    """
    driver = None
    session_data = None

    try:
        # Check if we have a shared driver from ScriptWeaver
        shared_driver = get_shared_driver()
        if shared_driver:
            print("PASS: Using shared browser session from ScriptWeaver")
            yield shared_driver
            return

        # Use embedded session data to recreate state
        if SESSION_DATA and SESSION_DATA.get("session_id"):
            session_data = SESSION_DATA

            # Create new driver instance
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-popup-blocking")

            driver = webdriver.Chrome(options=chrome_options)

            # Navigate to the preserved URL
            if session_data.get("current_url"):
                driver.get(session_data["current_url"])

            # Restore cookies
            if session_data.get("cookies"):
                for cookie in session_data["cookies"]:
                    try:
                        driver.add_cookie(cookie)
                    except Exception as e:
                        print(f"Could not restore cookie: {{e}}")

            # Restore local storage
            if session_data.get("local_storage"):
                for key, value in session_data["local_storage"].items():
                    try:
                        driver.execute_script("localStorage.setItem(arguments[0], arguments[1]);", key, value)
                    except Exception as e:
                        print(f"Could not restore local storage item: {{e}}")

            # Restore session storage
            if session_data.get("session_storage"):
                for key, value in session_data["session_storage"].items():
                    try:
                        driver.execute_script("sessionStorage.setItem(arguments[0], arguments[1]);", key, value)
                    except Exception as e:
                        print(f"Could not restore session storage item: {{e}}")

            print("PASS: Session continuity enabled - restored browser state from ScriptWeaver")

        else:
            # Fallback to standard browser creation
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-popup-blocking")

            driver = webdriver.Chrome(options=chrome_options)
            print("WARN: Session continuity not available - created new browser instance")

        yield driver

    except Exception as e:
        print(f"FAIL: Error in session browser setup: {{e}}")
        # Fallback to standard browser creation
        if not driver:
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            driver = webdriver.Chrome(options=chrome_options)
        yield driver

    finally:
        # Only quit the driver if it's not the shared one
        if driver and driver != get_shared_driver():
            try:
                driver.quit()
            except Exception as e:
                print(f"Error closing browser: {{e}}")

# Alias for backward compatibility
@pytest.fixture
def browser(browser_with_session):
    """Compatibility alias for the browser fixture."""
    return browser_with_session

# =========================================================================
# END SESSION CONTINUITY INJECTION
# =========================================================================

'''
        
        # Combine session injection with original content
        modified_content = session_injection_code + "\n" + original_content
        
        return modified_content

    def _execute_pytest_with_session(self) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Execute pytest with the session-aware script using direct execution.

        Returns:
            Tuple[bool, str, Dict]: (success, message, execution_results)
        """
        try:
            # For now, let's use a direct execution approach instead of subprocess
            # This allows us to maintain the browser session more effectively

            # Import the test module dynamically
            import importlib.util
            import sys

            # Load the modified script as a module
            spec = importlib.util.spec_from_file_location("test_module", self.modified_script_path)
            test_module = importlib.util.module_from_spec(spec)

            # Set the shared driver in the module's globals before execution
            test_module.__dict__['_shared_driver'] = self.driver

            # Add the module to sys.modules so imports work
            sys.modules["test_module"] = test_module

            # Execute the module
            spec.loader.exec_module(test_module)

            # Now run the tests using pytest programmatically
            import pytest

            # Prepare pytest arguments
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_xml_path = f"session_results_{timestamp}.xml"

            pytest_args = [
                self.modified_script_path,
                f"--junitxml={result_xml_path}",
                "--log-cli-level=INFO",
                "--capture=no",
                "--tb=short",
                "-v",
                "-s"  # Don't capture stdout
            ]

            # Set environment variables
            os.environ["HEADLESS"] = "0"
            os.environ["PYTEST_QUIET_MODE"] = "0"
            os.environ["SCRIPTWEAVER_SESSION_MODE"] = "1"

            logger.info(f"Executing pytest programmatically with session preservation")

            # Execute pytest programmatically
            exit_code = pytest.main(pytest_args)

            # Process results
            success = exit_code == 0

            # Parse execution results
            execution_results = {
                "return_code": exit_code,
                "xml_report_path": result_xml_path if os.path.exists(result_xml_path) else None,
                "execution_time": datetime.now().isoformat(),
                "session_preserved": True,
                "execution_method": "programmatic"
            }

            if success:
                message = "Script executed successfully with session preservation (programmatic)"
                logger.info(f"Pytest execution successful: {message}")
            else:
                message = f"Script execution failed (exit code: {exit_code})"
                logger.error(f"Pytest execution failed: {message}")

            return success, message, execution_results

        except Exception as e:
            logger.error(f"Error executing pytest with session: {e}")
            # Fallback to subprocess execution
            return self._execute_pytest_subprocess_fallback()

    def _execute_pytest_subprocess_fallback(self) -> Tuple[bool, str, Dict[str, Any]]:
        """Fallback to subprocess execution if programmatic execution fails."""
        try:
            logger.info("Falling back to subprocess execution")

            # Prepare pytest command
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_xml_path = f"session_results_{timestamp}.xml"

            pytest_command = [
                "pytest",
                self.modified_script_path,
                f"--junitxml={result_xml_path}",
                "--log-cli-level=INFO",
                "--capture=no",
                "--tb=short",
                "-v"
            ]

            # Set environment variables
            env = os.environ.copy()
            env["HEADLESS"] = "0"
            env["PYTEST_QUIET_MODE"] = "0"
            env["SCRIPTWEAVER_SESSION_MODE"] = "1"

            # Execute pytest
            result = subprocess.run(
                pytest_command,
                capture_output=True,
                text=True,
                env=env,
                cwd=os.getcwd()
            )

            # Process results
            success = result.returncode == 0

            execution_results = {
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "xml_report_path": result_xml_path if os.path.exists(result_xml_path) else None,
                "execution_time": datetime.now().isoformat(),
                "session_preserved": False,  # Subprocess can't preserve session
                "execution_method": "subprocess_fallback"
            }

            if success:
                message = "Script executed successfully (subprocess fallback)"
            else:
                message = f"Script execution failed (return code: {result.returncode})"
                logger.error(f"STDERR: {result.stderr}")

            return success, message, execution_results

        except Exception as e:
            logger.error(f"Error in subprocess fallback: {e}")
            return False, f"Subprocess execution error: {e}", {}

    def _cleanup_temp_files(self):
        """Clean up temporary files created during session execution."""
        try:
            # Clean up session data file
            if self.session_data_file and os.path.exists(self.session_data_file):
                os.unlink(self.session_data_file)
                logger.debug(f"Cleaned up session data file: {self.session_data_file}")

            # Clean up modified script file
            if self.modified_script_path and os.path.exists(self.modified_script_path):
                os.unlink(self.modified_script_path)
                logger.debug(f"Cleaned up modified script file: {self.modified_script_path}")

        except Exception as e:
            logger.warning(f"Error during temp file cleanup: {e}")


class SessionBrowserManager:
    """
    Manages browser session handoff between ScriptWeaver and pytest execution.

    This class provides utilities for preserving and restoring browser sessions
    across different execution contexts.
    """

    @staticmethod
    def preserve_session_state(driver: WebDriver) -> Dict[str, Any]:
        """
        Preserve the current browser session state for later restoration.

        Args:
            driver: WebDriver instance to preserve

        Returns:
            Dict containing preserved session state
        """
        try:
            state = {
                "url": driver.current_url,
                "title": driver.title,
                "cookies": driver.get_cookies(),
                "window_size": driver.get_window_size(),
                "window_position": driver.get_window_position(),
                "local_storage": {},
                "session_storage": {},
                "timestamp": datetime.now().isoformat()
            }

            # Preserve local storage
            try:
                local_storage = driver.execute_script("""
                    var storage = {};
                    for (var i = 0; i < localStorage.length; i++) {
                        var key = localStorage.key(i);
                        storage[key] = localStorage.getItem(key);
                    }
                    return storage;
                """)
                state["local_storage"] = local_storage
            except Exception as e:
                logger.warning(f"Could not preserve local storage: {e}")

            # Preserve session storage
            try:
                session_storage = driver.execute_script("""
                    var storage = {};
                    for (var i = 0; i < sessionStorage.length; i++) {
                        var key = sessionStorage.key(i);
                        storage[key] = sessionStorage.getItem(key);
                    }
                    return storage;
                """)
                state["session_storage"] = session_storage
            except Exception as e:
                logger.warning(f"Could not preserve session storage: {e}")

            return state

        except Exception as e:
            logger.error(f"Error preserving session state: {e}")
            return {}

    @staticmethod
    def restore_session_state(driver: WebDriver, state: Dict[str, Any]) -> bool:
        """
        Restore browser session state from preserved data.

        Args:
            driver: WebDriver instance to restore state to
            state: Preserved session state data

        Returns:
            bool: True if restoration was successful
        """
        try:
            # Navigate to preserved URL
            if state.get("url"):
                driver.get(state["url"])

            # Restore cookies
            if state.get("cookies"):
                for cookie in state["cookies"]:
                    try:
                        driver.add_cookie(cookie)
                    except Exception as e:
                        logger.warning(f"Could not restore cookie {cookie.get('name', 'unknown')}: {e}")

            # Restore window size and position
            if state.get("window_size"):
                try:
                    driver.set_window_size(state["window_size"]["width"], state["window_size"]["height"])
                except Exception as e:
                    logger.warning(f"Could not restore window size: {e}")

            if state.get("window_position"):
                try:
                    driver.set_window_position(state["window_position"]["x"], state["window_position"]["y"])
                except Exception as e:
                    logger.warning(f"Could not restore window position: {e}")

            # Restore local storage
            if state.get("local_storage"):
                for key, value in state["local_storage"].items():
                    try:
                        driver.execute_script("localStorage.setItem(arguments[0], arguments[1]);", key, value)
                    except Exception as e:
                        logger.warning(f"Could not restore local storage item {key}: {e}")

            # Restore session storage
            if state.get("session_storage"):
                for key, value in state["session_storage"].items():
                    try:
                        driver.execute_script("sessionStorage.setItem(arguments[0], arguments[1]);", key, value)
                    except Exception as e:
                        logger.warning(f"Could not restore session storage item {key}: {e}")

            logger.info("Session state restored successfully")
            return True

        except Exception as e:
            logger.error(f"Error restoring session state: {e}")
            return False

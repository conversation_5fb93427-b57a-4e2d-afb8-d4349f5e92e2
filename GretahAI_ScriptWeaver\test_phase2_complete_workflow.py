"""
Phase 2 Complete Workflow Test for GretahAI ScriptWeaver

This test validates the complete Phase 2 browser session continuity implementation:
- Enhanced script generation with session awareness
- Complete Stage 6→7→4 cycle integration
- Enhanced StateManager session persistence tracking
- Workflow integration with cycle management
- UI components for session continuity status

Test Scenarios:
1. Session-aware script generation
2. Complete workflow cycle (Stage 6→7→4)
3. Multiple cycle iterations with state preservation
4. Enhanced StateManager session tracking
5. Error handling and recovery mechanisms
6. UI component integration
"""

import os
import sys
import tempfile
import time
import logging
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.browser_session_manager import BrowserSessionManager
from core.workflow_integration import WorkflowBrowserIntegration
from core.session_aware_script_generator import SessionAwareScriptGenerator
from state_manager import StateManager
from core.ai_generation import generate_test_script

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("Phase2CompleteWorkflowTest")

def create_mock_test_case() -> dict:
    """Create a mock test case for testing."""
    return {
        "Test Case ID": "TC_PHASE2_001",
        "Test Case Name": "Phase 2 Session Continuity Test",
        "Test Steps": [
            {
                "Step": 1,
                "Action": "Navigate to test page",
                "Expected Result": "Page loads successfully"
            },
            {
                "Step": 2,
                "Action": "Set session state markers",
                "Expected Result": "State markers are preserved"
            },
            {
                "Step": 3,
                "Action": "Validate session continuity",
                "Expected Result": "Session state is maintained"
            }
        ]
    }

def test_session_aware_script_generation():
    """Test the enhanced session-aware script generation."""
    print("\nTest 1: Session-Aware Script Generation")
    print("=" * 50)
    
    try:
        # Create session-aware script generator
        print("1.1 Creating session-aware script generator...")
        generator = SessionAwareScriptGenerator()
        
        # Create mock test case
        test_case = create_mock_test_case()
        
        # Create basic script content
        basic_script = '''
def test_basic_functionality(browser):
    """Basic test function."""
    browser.get("https://example.com")
    assert "Example" in browser.title
'''
        
        print("1.2 Generating session-aware script...")
        enhanced_script, metadata = generator.generate_session_aware_script(
            basic_script, test_case, enable_continuity=True
        )
        
        # Validate enhanced script
        if metadata.get("session_aware"):
            print("PASS: Session-aware script generated successfully")
            print(f"     Features: {metadata.get('continuity_features', [])}")
        else:
            print("FAIL: Script generation did not enable session awareness")
            return False
        
        # Check for session continuity features in script
        required_features = [
            "validate_session_state",
            "preserve_test_state",
            "get_preserved_state",
            "SCRIPTWEAVER_SESSION_MODE"
        ]
        
        missing_features = []
        for feature in required_features:
            if feature not in enhanced_script:
                missing_features.append(feature)
        
        if missing_features:
            print(f"FAIL: Missing session continuity features: {missing_features}")
            return False
        else:
            print("PASS: All session continuity features present in generated script")
        
        print("PASS: Session-aware script generation test completed successfully")
        return True
        
    except Exception as e:
        print(f"FAIL: Session-aware script generation test failed: {e}")
        return False

def test_enhanced_state_manager():
    """Test the enhanced StateManager with Phase 2 features."""
    print("\nTest 2: Enhanced StateManager")
    print("=" * 50)
    
    try:
        print("2.1 Creating StateManager with Phase 2 features...")
        state = StateManager()
        
        # Test session persistence cycle
        print("2.2 Testing session persistence cycle...")
        state.start_session_persistence_cycle("https://example.com")
        
        if state.session_persistence_active:
            print("PASS: Session persistence cycle started")
        else:
            print("FAIL: Session persistence cycle not started")
            return False
        
        # Test session state update
        print("2.3 Testing session state update...")
        state.update_session_state_after_execution(
            success=True,
            url="https://example.com/page2",
            cookies_preserved=True,
            storage_preserved=True
        )
        
        if state.session_state_preserved and state.session_cookies_preserved:
            print("PASS: Session state updated successfully")
        else:
            print("FAIL: Session state update failed")
            return False
        
        # Test session validation
        print("2.4 Testing session validation...")
        state.browser_session_active = True  # Simulate active session
        is_valid, message = state.validate_session_continuity()
        
        if is_valid:
            print(f"PASS: Session validation successful: {message}")
        else:
            print(f"FAIL: Session validation failed: {message}")
            return False
        
        # Test continuity status
        print("2.5 Testing continuity status...")
        status = state.get_browser_continuity_status()
        
        expected_fields = [
            'persistence_active', 'state_preserved', 'cycle_count',
            'cookies_preserved', 'storage_preserved', 'navigation_history_count'
        ]
        
        missing_fields = []
        for field in expected_fields:
            if field not in status:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"FAIL: Missing status fields: {missing_fields}")
            return False
        else:
            print("PASS: All Phase 2 status fields present")
        
        print("PASS: Enhanced StateManager test completed successfully")
        return True
        
    except Exception as e:
        print(f"FAIL: Enhanced StateManager test failed: {e}")
        return False

def test_complete_workflow_cycle():
    """Test the complete Stage 6→7→4 workflow cycle."""
    print("\nTest 3: Complete Workflow Cycle")
    print("=" * 50)
    
    session_manager = BrowserSessionManager()
    integration = WorkflowBrowserIntegration()
    state = StateManager()
    
    try:
        print("3.1 Setting up browser session...")
        success = session_manager.create_session(headless=False)
        if not success:
            print("FAIL: Could not create browser session")
            return False
        
        session_manager.navigate_to("https://example.com")
        state.browser_session_active = True
        print("PASS: Browser session setup completed")
        
        # Initialize workflow integration with session
        integration.session_manager = session_manager
        
        print("3.2 Testing script execution with continuity...")
        
        # Create a test script
        test_script_content = '''
def test_workflow_cycle(browser):
    """Test workflow cycle functionality."""
    # Validate session state
    if not validate_session_state(browser):
        print("WARNING: Session validation failed")
    
    # Set cycle markers
    preserve_test_state(browser, "cycle_test", "active")
    preserve_test_state(browser, "cycle_count", "1")
    
    # Verify page state
    current_url = browser.current_url
    assert "example.com" in current_url
    
    print("PASS: Workflow cycle test completed")
'''
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(test_script_content)
            script_path = f.name
        
        # Execute script with continuity
        success, message, results = integration.execute_script_with_continuity(state, script_path)
        
        if success:
            print(f"PASS: Script execution with continuity successful")
            print(f"     Cycle count: {results.get('cycle_count', 0)}")
            print(f"     Session preserved: {results.get('session_preserved', False)}")
        else:
            print(f"FAIL: Script execution with continuity failed: {message}")
            return False
        
        print("3.3 Testing complete workflow cycle...")
        cycle_success, cycle_message, cycle_info = integration.complete_workflow_cycle(state)
        
        if cycle_success:
            print(f"PASS: Complete workflow cycle successful: {cycle_message}")
            print(f"     Cycle info: {cycle_info}")
        else:
            print(f"FAIL: Complete workflow cycle failed: {cycle_message}")
            return False
        
        # Test multiple cycles
        print("3.4 Testing multiple workflow cycles...")
        for i in range(2, 4):  # Cycles 2 and 3
            success, message, results = integration.execute_script_with_continuity(state, script_path)
            if success:
                cycle_success, cycle_message, cycle_info = integration.complete_workflow_cycle(state)
                if cycle_success:
                    print(f"PASS: Cycle #{i} completed successfully")
                else:
                    print(f"FAIL: Cycle #{i} failed: {cycle_message}")
                    return False
            else:
                print(f"FAIL: Script execution for cycle #{i} failed: {message}")
                return False
        
        # Verify final state
        final_status = state.get_browser_continuity_status()
        if final_status.get('cycle_count', 0) >= 3:
            print(f"PASS: Multiple cycles completed (count: {final_status['cycle_count']})")
        else:
            print(f"FAIL: Expected at least 3 cycles, got {final_status.get('cycle_count', 0)}")
            return False
        
        # Clean up
        os.unlink(script_path)
        session_manager.cleanup_session()
        
        print("PASS: Complete workflow cycle test completed successfully")
        return True
        
    except Exception as e:
        print(f"FAIL: Complete workflow cycle test failed: {e}")
        session_manager.cleanup_session()
        return False

def test_error_handling_and_recovery():
    """Test error handling and recovery mechanisms."""
    print("\nTest 4: Error Handling and Recovery")
    print("=" * 50)
    
    state = StateManager()
    
    try:
        print("4.1 Testing session validation with errors...")
        
        # Test with inactive session
        state.browser_session_active = False
        is_valid, message = state.validate_session_continuity()
        
        if not is_valid and "not active" in message:
            print("PASS: Inactive session properly detected")
        else:
            print("FAIL: Inactive session not properly detected")
            return False
        
        # Test with excessive error recovery attempts
        print("4.2 Testing excessive error recovery detection...")
        state.browser_session_active = True
        state.session_persistence_active = True
        state.session_error_recovery_count = 5  # Exceed threshold
        
        is_valid, message = state.validate_session_continuity()
        
        if not is_valid and "error recovery" in message:
            print("PASS: Excessive error recovery properly detected")
        else:
            print("FAIL: Excessive error recovery not properly detected")
            return False
        
        # Test session reset
        print("4.3 Testing session continuity reset...")
        state.reset_session_continuity_state("Test reset")
        
        if (not state.session_persistence_active and 
            state.session_cycle_count == 0 and
            state.session_error_recovery_count == 0):
            print("PASS: Session continuity reset successful")
        else:
            print("FAIL: Session continuity reset failed")
            return False
        
        print("PASS: Error handling and recovery test completed successfully")
        return True
        
    except Exception as e:
        print(f"FAIL: Error handling and recovery test failed: {e}")
        return False

def test_ui_component_integration():
    """Test UI component integration (import and basic functionality)."""
    print("\nTest 5: UI Component Integration")
    print("=" * 50)
    
    try:
        print("5.1 Testing UI component imports...")
        
        # Test enhanced workflow indicator import
        try:
            from ui_components.browser_continuity_components import render_enhanced_continuity_workflow_indicator
            print("PASS: Enhanced workflow indicator imported successfully")
        except ImportError as e:
            print(f"FAIL: Enhanced workflow indicator import failed: {e}")
            return False
        
        # Test other UI component imports
        try:
            from ui_components.browser_continuity_components import (
                render_enhanced_element_selection_ui,
                render_script_execution_continuity_ui,
                render_session_recovery_ui
            )
            print("PASS: All UI components imported successfully")
        except ImportError as e:
            print(f"FAIL: UI component import failed: {e}")
            return False
        
        print("5.2 Testing UI component functionality...")
        
        # Create mock cycle info
        cycle_info = {
            "cycle_count": 3,
            "navigation_history_count": 5,
            "error_recovery_count": 1,
            "validation_failures": 0,
            "state_preserved": True,
            "cookies_preserved": True,
            "storage_preserved": True
        }
        
        # Test that the function can be called without errors
        # (We can't actually render in test environment, but we can test the function exists)
        if callable(render_enhanced_continuity_workflow_indicator):
            print("PASS: Enhanced workflow indicator is callable")
        else:
            print("FAIL: Enhanced workflow indicator is not callable")
            return False
        
        print("PASS: UI component integration test completed successfully")
        return True
        
    except Exception as e:
        print(f"FAIL: UI component integration test failed: {e}")
        return False

def main():
    """Run complete Phase 2 workflow tests."""
    print("Phase 2 Complete Workflow Tests for GretahAI ScriptWeaver")
    print("=" * 70)
    
    test_results = []
    
    try:
        # Test 1: Session-aware script generation
        result1 = test_session_aware_script_generation()
        test_results.append(("Session-Aware Script Generation", result1))
        
        # Test 2: Enhanced StateManager
        result2 = test_enhanced_state_manager()
        test_results.append(("Enhanced StateManager", result2))
        
        # Test 3: Complete workflow cycle
        result3 = test_complete_workflow_cycle()
        test_results.append(("Complete Workflow Cycle", result3))
        
        # Test 4: Error handling and recovery
        result4 = test_error_handling_and_recovery()
        test_results.append(("Error Handling and Recovery", result4))
        
        # Test 5: UI component integration
        result5 = test_ui_component_integration()
        test_results.append(("UI Component Integration", result5))
        
    except Exception as e:
        print(f"FAIL: Test suite failed with error: {e}")
        test_results.append(("Test Suite", False))
    
    # Print results summary
    print("\n" + "=" * 70)
    print("Phase 2 Complete Workflow Test Results:")
    print("=" * 70)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<40} {status}")
        if result:
            passed += 1
    
    print("=" * 70)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("\nSUCCESS: Phase 2 implementation is complete and functional!")
        print("✓ Session-aware script generation working")
        print("✓ Complete Stage 6→7→4 cycle integration functional")
        print("✓ Enhanced StateManager session tracking operational")
        print("✓ Workflow integration with cycle management working")
        print("✓ Error handling and recovery mechanisms functional")
        print("✓ UI component integration ready")
        print("\nPhase 2 browser session continuity is ready for production use!")
        return True
    else:
        print(f"\nFAILURE: {total - passed} tests failed. Phase 2 needs additional work.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

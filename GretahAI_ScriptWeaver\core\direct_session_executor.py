"""
Direct Session Executor for GretahAI ScriptWeaver

This module provides a simplified approach to executing test scripts while
preserving browser sessions. Instead of complex injection mechanisms, it
directly executes test functions using the existing WebDriver instance.

Key Features:
- Direct execution of test functions with existing WebDriver
- No complex script modification or injection required
- Maintains browser state throughout execution
- Simple error handling and reporting
"""

import os
import sys
import ast
import inspect
import logging
from typing import Tuple, Dict, Any, List, Callable
from selenium.webdriver.chrome.webdriver import WebDriver
from datetime import datetime

logger = logging.getLogger("ScriptWeaver.direct_session_executor")

class DirectSessionExecutor:
    """
    Executes test scripts directly using an existing WebDriver instance.
    
    This class provides a simplified approach to session continuity by
    directly executing test functions rather than using pytest subprocess.
    """
    
    def __init__(self, driver: WebDriver, session_id: str):
        """
        Initialize the direct session executor.
        
        Args:
            driver: Existing WebDriver instance to preserve
            session_id: Session ID for tracking
        """
        self.driver = driver
        self.session_id = session_id
        self.test_results = []
        
    def execute_script(self, script_path: str) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Execute a test script directly with session preservation.
        
        Args:
            script_path: Path to the script file to execute
            
        Returns:
            Tuple[bool, str, Dict]: (success, message, execution_results)
        """
        try:
            # Validate inputs
            if not os.path.exists(script_path):
                return False, f"Script file not found: {script_path}", {}
            
            if not self._validate_driver():
                return False, "Browser session is not valid or responsive", {}
            
            # Extract and execute test functions
            test_functions = self._extract_test_functions(script_path)
            
            if not test_functions:
                return False, "No test functions found in script", {}
            
            # Execute each test function
            execution_results = self._execute_test_functions(test_functions)
            
            # Determine overall success
            total_tests = len(execution_results["test_results"])
            passed_tests = sum(1 for result in execution_results["test_results"] if result["passed"])
            
            success = passed_tests == total_tests
            message = f"Executed {total_tests} tests: {passed_tests} passed, {total_tests - passed_tests} failed"
            
            logger.info(f"Direct execution completed: {message}")
            return success, message, execution_results
            
        except Exception as e:
            logger.error(f"Error in direct script execution: {e}")
            return False, f"Direct execution failed: {e}", {}
    
    def _validate_driver(self) -> bool:
        """Validate that the WebDriver instance is still active and responsive."""
        try:
            current_url = self.driver.current_url
            ready_state = self.driver.execute_script("return document.readyState;")
            logger.debug(f"Driver validation - URL: {current_url}, Ready state: {ready_state}")
            return True
        except Exception as e:
            logger.error(f"Driver validation failed: {e}")
            return False
    
    def _extract_test_functions(self, script_path: str) -> List[Callable]:
        """
        Extract test functions from the script file.
        
        Args:
            script_path: Path to the script file
            
        Returns:
            List of test functions
        """
        try:
            # Read the script content
            with open(script_path, 'r') as f:
                script_content = f.read()
            
            # Parse the AST to find test functions
            tree = ast.parse(script_content)
            test_function_names = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef) and node.name.startswith('test_'):
                    test_function_names.append(node.name)
            
            if not test_function_names:
                logger.warning("No test functions found in script")
                return []
            
            # Execute the script to get the function objects
            script_globals = {'browser': self.driver}
            exec(script_content, script_globals)
            
            # Extract the test function objects
            test_functions = []
            for func_name in test_function_names:
                if func_name in script_globals:
                    test_functions.append(script_globals[func_name])
                    logger.debug(f"Found test function: {func_name}")
            
            return test_functions
            
        except Exception as e:
            logger.error(f"Error extracting test functions: {e}")
            return []
    
    def _execute_test_functions(self, test_functions: List[Callable]) -> Dict[str, Any]:
        """
        Execute the test functions directly.
        
        Args:
            test_functions: List of test functions to execute
            
        Returns:
            Dict containing execution results
        """
        execution_start = datetime.now()
        test_results = []
        
        for test_func in test_functions:
            test_result = self._execute_single_test(test_func)
            test_results.append(test_result)
        
        execution_end = datetime.now()
        execution_time = (execution_end - execution_start).total_seconds()
        
        return {
            "execution_time": execution_time,
            "execution_start": execution_start.isoformat(),
            "execution_end": execution_end.isoformat(),
            "test_results": test_results,
            "session_preserved": True,
            "execution_method": "direct"
        }
    
    def _execute_single_test(self, test_func: Callable) -> Dict[str, Any]:
        """
        Execute a single test function.
        
        Args:
            test_func: Test function to execute
            
        Returns:
            Dict containing test result
        """
        test_start = datetime.now()
        test_name = test_func.__name__
        
        try:
            # Check if function expects browser parameter
            sig = inspect.signature(test_func)
            if 'browser' in sig.parameters:
                test_func(self.driver)
            else:
                test_func()
            
            test_end = datetime.now()
            test_time = (test_end - test_start).total_seconds()
            
            result = {
                "test_name": test_name,
                "passed": True,
                "error": None,
                "execution_time": test_time,
                "start_time": test_start.isoformat(),
                "end_time": test_end.isoformat()
            }
            
            logger.info(f"Test {test_name} PASSED ({test_time:.2f}s)")
            return result
            
        except Exception as e:
            test_end = datetime.now()
            test_time = (test_end - test_start).total_seconds()
            
            result = {
                "test_name": test_name,
                "passed": False,
                "error": str(e),
                "execution_time": test_time,
                "start_time": test_start.isoformat(),
                "end_time": test_end.isoformat()
            }
            
            logger.error(f"Test {test_name} FAILED ({test_time:.2f}s): {e}")
            return result
    
    def get_session_info(self) -> Dict[str, Any]:
        """Get current session information."""
        try:
            return {
                "session_id": self.session_id,
                "current_url": self.driver.current_url,
                "page_title": self.driver.title,
                "window_handles": len(self.driver.window_handles),
                "active": True
            }
        except Exception as e:
            logger.error(f"Error getting session info: {e}")
            return {
                "session_id": self.session_id,
                "active": False,
                "error": str(e)
            }


class SimplifiedSessionRunner:
    """
    Simplified session runner that uses direct execution instead of pytest.
    
    This provides a more reliable approach to session continuity by avoiding
    the complexity of subprocess execution and script injection.
    """
    
    def __init__(self, driver: WebDriver, session_id: str):
        """Initialize the simplified session runner."""
        self.driver = driver
        self.session_id = session_id
        self.executor = DirectSessionExecutor(driver, session_id)
    
    def execute_script_with_session(self, script_path: str) -> Tuple[bool, str]:
        """
        Execute a script with session preservation using direct execution.
        
        Args:
            script_path: Path to the script file to execute
            
        Returns:
            Tuple[bool, str]: (success, message)
        """
        try:
            success, message, results = self.executor.execute_script(script_path)
            
            if success:
                test_count = len(results.get("test_results", []))
                execution_time = results.get("execution_time", 0)
                detailed_message = f"{message} (execution time: {execution_time:.2f}s)"
                return True, detailed_message
            else:
                return False, message
                
        except Exception as e:
            logger.error(f"Error in simplified session execution: {e}")
            return False, f"Simplified execution failed: {e}"
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """Get summary of the last execution."""
        return self.executor.get_session_info()

"""
Quick Validation Test for Session Continuity

This test performs a quick validation of the core session continuity functionality
to ensure the basic implementation is working before running comprehensive tests.
"""

import os
import sys
import tempfile
import logging

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.browser_session_manager import BrowserSessionManager
from core.direct_session_executor import DirectSessionExecutor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("QuickValidation")

def create_simple_test() -> str:
    """Create a very simple test for validation."""
    test_content = '''
def test_simple_check(browser):
    """Simple test to verify browser functionality."""
    try:
        url = browser.current_url
        title = browser.title
        print(f"URL: {url}")
        print(f"Title: {title}")
        
        # Set a simple marker
        browser.execute_script("localStorage.setItem('quick_test', 'success');")
        
        # Verify marker
        marker = browser.execute_script("return localStorage.getItem('quick_test');")
        assert marker == 'success'
        
        print("PASS: Simple test completed")
        
    except Exception as e:
        print(f"FAIL: Simple test failed: {e}")
        raise
'''
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_content)
        return f.name

def test_basic_functionality():
    """Test basic session functionality."""
    print("Quick Validation Test")
    print("=" * 30)
    
    session_manager = BrowserSessionManager()
    
    try:
        print("1. Creating session...")
        success = session_manager.create_session(headless=False)
        if not success:
            print("FAIL: Could not create session")
            return False
        print("PASS: Session created")
        
        print("2. Navigating...")
        nav_success = session_manager.navigate_to("https://example.com")
        if not nav_success:
            print("FAIL: Could not navigate")
            return False
        print("PASS: Navigation successful")
        
        print("3. Testing direct executor...")
        executor = DirectSessionExecutor(session_manager.driver, session_manager.session_id)
        
        script_path = create_simple_test()
        success, message, results = executor.execute_script(script_path)
        
        if success:
            print(f"PASS: Direct execution successful: {message}")
        else:
            print(f"FAIL: Direct execution failed: {message}")
            return False
        
        print("4. Testing session manager execution...")
        success, message = session_manager.execute_script_with_session(script_path)
        
        if success:
            print(f"PASS: Session manager execution successful")
        else:
            print(f"FAIL: Session manager execution failed: {message}")
            return False
        
        # Verify state
        marker = session_manager.driver.execute_script("return localStorage.getItem('quick_test');")
        if marker == 'success':
            print("PASS: State preserved")
        else:
            print(f"FAIL: State not preserved: {marker}")
            return False
        
        # Cleanup
        os.unlink(script_path)
        session_manager.cleanup_session()
        
        print("SUCCESS: All quick validation tests passed!")
        return True
        
    except Exception as e:
        print(f"FAIL: Quick validation failed: {e}")
        session_manager.cleanup_session()
        return False

if __name__ == "__main__":
    success = test_basic_functionality()
    sys.exit(0 if success else 1)

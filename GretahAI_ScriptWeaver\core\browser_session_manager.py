"""
Browser Session Manager for GretahAI ScriptWeaver

This module provides browser state continuity across workflow stages,
enabling seamless element selection after script execution without
losing DOM state or page context.

Key Features:
- Persistent browser sessions across workflow stages
- State preservation during Stage 6→7→4 transitions
- Resource management and cleanup
- Integration with existing interactive selector
- Session recovery and error handling
"""

import logging
import time
import threading
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any, Tuple
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import WebDriverException, TimeoutException
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger("ScriptWeaver.browser_session_manager")

class BrowserSessionManager:
    """
    Manages persistent browser sessions for workflow continuity.
    
    This class maintains browser instances across multiple workflow stages,
    preserving DOM state and page context for seamless element selection.
    """
    
    def __init__(self):
        self.driver: Optional[webdriver.Chrome] = None
        self.session_id: Optional[str] = None
        self.is_active: bool = False
        self.current_url: Optional[str] = None
        self.created_at: Optional[datetime] = None
        self.last_activity: Optional[datetime] = None
        self.cleanup_timer: Optional[threading.Timer] = None
        self.session_timeout: int = 1800  # 30 minutes default timeout
        
    def create_session(self, headless: bool = False, timeout: int = 1800) -> bool:
        """
        Create a new browser session with anti-detection measures.
        
        Args:
            headless: Whether to run browser in headless mode
            timeout: Session timeout in seconds
            
        Returns:
            bool: True if session created successfully
        """
        try:
            # Clean up existing session if any
            if self.is_active:
                self.cleanup_session()
            
            # Configure Chrome options
            chrome_options = Options()
            
            if headless:
                chrome_options.add_argument("--headless")
                chrome_options.add_argument("--window-size=1920,1080")
            else:
                chrome_options.add_argument("--start-maximized")
            
            # Standard options for stability
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-popup-blocking")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-gpu")
            
            # Enable performance and network logging
            chrome_options.set_capability("goog:loggingPrefs", {
                'browser': 'ALL',
                'performance': 'ALL',
                'network': 'ALL'
            })
            
            # Create WebDriver instance
            self.driver = webdriver.Chrome(
                service=Service(ChromeDriverManager().install()),
                options=chrome_options
            )
            
            # Apply anti-detection measures
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                "userAgent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })
            
            # Set implicit wait and configure session
            self.driver.implicitly_wait(10)
            self.session_id = self.driver.session_id
            self.is_active = True
            self.created_at = datetime.now()
            self.last_activity = datetime.now()
            self.session_timeout = timeout
            
            # Start cleanup timer
            self._start_cleanup_timer()
            
            logger.info(f"Browser session created successfully. Session ID: {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create browser session: {e}")
            self.cleanup_session()
            return False
    
    def navigate_to(self, url: str) -> bool:
        """
        Navigate to a specific URL while maintaining session.
        
        Args:
            url: Target URL to navigate to
            
        Returns:
            bool: True if navigation successful
        """
        if not self._validate_session():
            return False
        
        try:
            self.driver.get(url)
            self.current_url = url
            self._update_activity()
            
            # Wait for page to load
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            logger.info(f"Successfully navigated to: {url}")
            return True
            
        except TimeoutException:
            logger.error(f"Timeout while navigating to {url}")
            return False
        except WebDriverException as e:
            logger.error(f"WebDriver error during navigation to {url}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during navigation to {url}: {e}")
            return False
    
    def execute_script_with_session(self, script_path: str) -> Tuple[bool, str]:
        """
        Execute a test script while preserving the browser session.

        This method executes the generated test script while maintaining
        the browser session for state continuity using direct execution.

        Args:
            script_path: Path to the script file to execute

        Returns:
            Tuple[bool, str]: (success, message)
        """
        if not self._validate_session():
            return False, "Browser session not active"

        try:
            self._update_activity()

            # Try simplified direct execution first
            try:
                from core.direct_session_executor import SimplifiedSessionRunner

                # Create simplified session runner
                runner = SimplifiedSessionRunner(self.driver, self.session_id)

                # Execute script with session preservation
                success, message = runner.execute_script_with_session(script_path)

                if success:
                    # Update session state after successful execution
                    self._update_activity()
                    self.current_url = self.driver.current_url
                    logger.info(f"Script executed successfully with direct session preservation: {message}")
                    return True, f"Script executed successfully with session preservation. {message}"
                else:
                    logger.warning(f"Direct execution failed, trying fallback: {message}")
                    # Fall through to pytest fallback

            except ImportError as e:
                logger.warning(f"Direct session executor not available: {e}")
                # Fall through to pytest fallback

            # Fallback to pytest-based execution
            try:
                from core.session_pytest_runner import SessionPytestRunner

                # Create session-aware pytest runner
                runner = SessionPytestRunner(self.driver, self.session_id)

                # Execute script with session preservation
                success, message, results = runner.execute_script(script_path)

                if success:
                    # Update session state after successful execution
                    self._update_activity()
                    self.current_url = self.driver.current_url
                    logger.info(f"Script executed successfully with pytest session preservation: {message}")
                    return True, f"Script executed successfully with session preservation. {message}"
                else:
                    logger.error(f"Pytest execution failed: {message}")
                    return False, f"Script execution failed: {message}"

            except ImportError as e:
                logger.error(f"Failed to import session pytest runner: {e}")
                return False, "No session execution methods available"

        except Exception as e:
            logger.error(f"Error during script execution: {e}")
            return False, f"Script execution failed: {e}"
    
    def prepare_for_element_selection(self) -> bool:
        """
        Prepare the browser session for interactive element selection.

        Returns:
            bool: True if preparation successful
        """
        if not self._validate_session():
            return False

        try:
            # Ensure we're on a valid page
            if not self.current_url:
                logger.warning("No current URL set for element selection")
                return False

            # Refresh activity timestamp
            self._update_activity()

            # Verify page is still responsive
            ready_state = self.driver.execute_script("return document.readyState;")
            if ready_state != "complete":
                logger.warning(f"Page not fully loaded, ready state: {ready_state}")

            # Scroll to top to ensure consistent starting position
            self.driver.execute_script("window.scrollTo(0, 0);")

            logger.info("Browser session prepared for element selection")
            return True

        except WebDriverException as e:
            logger.error(f"Browser session not responsive: {e}")
            return False
        except Exception as e:
            logger.error(f"Error preparing for element selection: {e}")
            return False

    def launch_element_selector(self) -> Tuple[Optional[Dict[str, Any]], str]:
        """
        Launch interactive element selector with preserved browser state.

        This method provides element selection using the existing browser session,
        eliminating the need for browser initialization and page navigation.

        Returns:
            Tuple[Optional[Dict], str]: (selected_element_info, message)
        """
        if not self._validate_session():
            return None, "Browser session not active or not responsive"

        try:
            # Prepare session for element selection
            if not self.prepare_for_element_selection():
                return None, "Failed to prepare browser session for element selection"

            # Import element selection functions
            from core.interactive_selector import inject_element_selection_script, get_selected_element_info

            # Inject element selection script
            if not inject_element_selection_script(self.driver):
                return None, "Failed to inject element selection script"

            logger.info("Element selector launched with preserved browser state")

            # Wait for element selection with timeout
            max_wait_time = 60  # seconds
            poll_interval = 0.5
            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                selected_element_info = get_selected_element_info(self.driver)
                if selected_element_info:
                    self._update_activity()
                    logger.info("Element selected successfully with preserved state")
                    return selected_element_info, "Element selected successfully"

                time.sleep(poll_interval)

            return None, "Element selection timed out after 60 seconds"

        except Exception as e:
            logger.error(f"Error in element selector launch: {e}")
            return None, f"Error launching element selector: {e}"
    
    def get_session_info(self) -> Dict[str, Any]:
        """
        Get comprehensive session information including performance metrics.

        Returns:
            Dict containing detailed session information and health metrics
        """
        if not self.is_active or not self.driver:
            return {
                "active": False,
                "session_id": None,
                "current_url": None,
                "created_at": None,
                "last_activity": None,
                "time_remaining": None,
                "error": "Session not active"
            }

        try:
            # Calculate time metrics
            time_remaining = None
            session_age_minutes = 0
            if self.created_at and self.session_timeout:
                elapsed = (datetime.now() - self.created_at).total_seconds()
                time_remaining = max(0, self.session_timeout - elapsed)
                session_age_minutes = elapsed / 60

            # Get basic session information
            current_url = self.driver.current_url
            title = self.driver.title

            # Calculate health score based on various factors
            health_score = self._calculate_health_score(time_remaining, session_age_minutes)

            # Get performance metrics
            performance_metrics = self._get_performance_metrics()

            session_info = {
                "active": True,
                "session_id": self.session_id,
                "current_url": current_url,
                "title": title,
                "created_at": self.created_at.isoformat() if self.created_at else None,
                "last_activity": self.last_activity.isoformat() if self.last_activity else None,
                "time_remaining": time_remaining,
                "session_age_minutes": session_age_minutes,
                "window_handles": len(self.driver.window_handles),
                "health_score": health_score,
                **performance_metrics
            }

            return session_info

        except WebDriverException as e:
            # Session might be dead
            logger.warning(f"Session validation failed during info retrieval: {e}")
            self.is_active = False
            return {
                "active": False,
                "error": "Session appears to be dead",
                "session_id": self.session_id,
                "health_score": 0
            }
        except Exception as e:
            logger.error(f"Error getting session info: {e}")
            return {
                "active": False,
                "error": str(e),
                "session_id": self.session_id,
                "health_score": 0
            }

    def _calculate_health_score(self, time_remaining: Optional[float], session_age_minutes: float) -> int:
        """
        Calculate session health score based on various factors.

        Args:
            time_remaining: Time remaining in seconds
            session_age_minutes: Session age in minutes

        Returns:
            int: Health score from 0-100
        """
        score = 100

        # Deduct points based on time remaining
        if time_remaining is not None:
            if time_remaining < 300:  # Less than 5 minutes
                score -= 40
            elif time_remaining < 900:  # Less than 15 minutes
                score -= 20
            elif time_remaining < 1800:  # Less than 30 minutes
                score -= 10

        # Deduct points based on session age
        if session_age_minutes > 60:  # More than 1 hour
            score -= 30
        elif session_age_minutes > 45:  # More than 45 minutes
            score -= 15
        elif session_age_minutes > 30:  # More than 30 minutes
            score -= 5

        # Additional checks for responsiveness
        try:
            # Quick responsiveness test
            self.driver.execute_script("return document.readyState;")
        except:
            score -= 25  # Browser not responsive

        return max(0, min(100, score))

    def _get_performance_metrics(self) -> Dict[str, Any]:
        """
        Get browser performance metrics.

        Returns:
            Dict containing performance metrics
        """
        metrics = {
            "memory_usage_mb": 0,
            "cpu_usage_percent": 0,
            "last_page_load_time": 0
        }

        try:
            # Try to get performance metrics from browser
            performance_logs = self.driver.get_log('performance')

            # Calculate memory usage (simplified estimation)
            # In a real implementation, this would use Chrome DevTools Protocol
            metrics["memory_usage_mb"] = len(performance_logs) * 0.5  # Rough estimation

            # CPU usage estimation based on activity
            if self.last_activity:
                time_since_activity = (datetime.now() - self.last_activity).total_seconds()
                if time_since_activity < 60:
                    metrics["cpu_usage_percent"] = 15.0  # Active usage
                elif time_since_activity < 300:
                    metrics["cpu_usage_percent"] = 5.0   # Light usage
                else:
                    metrics["cpu_usage_percent"] = 1.0   # Idle

            # Page load time estimation
            try:
                navigation_timing = self.driver.execute_script("""
                    return performance.timing.loadEventEnd - performance.timing.navigationStart;
                """)
                if navigation_timing > 0:
                    metrics["last_page_load_time"] = navigation_timing / 1000.0  # Convert to seconds
            except:
                pass

        except Exception as e:
            logger.debug(f"Could not retrieve performance metrics: {e}")

        return metrics
    
    def extend_session(self, additional_time: int = 1800) -> bool:
        """
        Extend the session timeout.
        
        Args:
            additional_time: Additional time in seconds
            
        Returns:
            bool: True if extension successful
        """
        if not self._validate_session():
            return False
        
        try:
            self.session_timeout += additional_time
            self._update_activity()
            self._restart_cleanup_timer()
            
            logger.info(f"Session extended by {additional_time} seconds")
            return True
            
        except Exception as e:
            logger.error(f"Error extending session: {e}")
            return False
    
    def cleanup_session(self) -> None:
        """Clean up the browser session and resources."""
        try:
            # Cancel cleanup timer
            if self.cleanup_timer:
                self.cleanup_timer.cancel()
                self.cleanup_timer = None
            
            # Close browser
            if self.driver:
                try:
                    self.driver.quit()
                except Exception as e:
                    logger.warning(f"Error during driver cleanup: {e}")
                finally:
                    self.driver = None
            
            # Reset state
            self.session_id = None
            self.is_active = False
            self.current_url = None
            self.created_at = None
            self.last_activity = None
            
            logger.info("Browser session cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Error during session cleanup: {e}")
    
    def _validate_session(self) -> bool:
        """Validate that the session is active and responsive."""
        if not self.is_active or not self.driver:
            return False
        
        try:
            # Quick responsiveness check
            self.driver.current_url
            return True
        except WebDriverException:
            logger.warning("Session validation failed - marking as inactive")
            self.is_active = False
            return False
    
    def _update_activity(self) -> None:
        """Update the last activity timestamp."""
        self.last_activity = datetime.now()
    
    def _start_cleanup_timer(self) -> None:
        """Start the automatic cleanup timer."""
        if self.cleanup_timer:
            self.cleanup_timer.cancel()
        
        self.cleanup_timer = threading.Timer(self.session_timeout, self._timeout_cleanup)
        self.cleanup_timer.start()
    
    def _restart_cleanup_timer(self) -> None:
        """Restart the cleanup timer with updated timeout."""
        self._start_cleanup_timer()
    
    def _timeout_cleanup(self) -> None:
        """Handle session timeout cleanup."""
        logger.info("Session timeout reached - cleaning up")
        self.cleanup_session()
    
    def __del__(self):
        """Ensure cleanup on object destruction."""
        self.cleanup_session()

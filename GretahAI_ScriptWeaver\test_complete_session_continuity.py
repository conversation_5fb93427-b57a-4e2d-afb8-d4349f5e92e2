"""
Complete Session Continuity Test for GretahAI ScriptWeaver Phase 1

This test validates the complete session continuity workflow:
1. Browser session creation and management
2. Direct session-aware script execution
3. State preservation during script execution
4. Session handoff between ScriptWeaver and test execution
5. Workflow integration testing

Test Scenarios:
- Basic session creation and navigation
- Direct script execution with session preservation
- State preservation across multiple script executions
- Workflow integration with StateManager
- Error handling and fallback mechanisms
"""

import os
import sys
import tempfile
import time
import logging
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.browser_session_manager import BrowserSessionManager
from core.workflow_integration import WorkflowBrowserIntegration
from core.direct_session_executor import DirectSessionExecutor, SimplifiedSessionRunner

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("CompleteSessionContinuityTest")

def create_comprehensive_test_script() -> str:
    """Create a comprehensive test script for session continuity testing."""
    test_script_content = '''
def test_page_navigation(browser):
    """Test basic page navigation and state setup."""
    try:
        # Get current URL and verify we're on the expected page
        current_url = browser.current_url
        print(f"Current URL: {current_url}")
        
        # Verify page is loaded
        title = browser.title
        print(f"Page title: {title}")
        assert title is not None and len(title) > 0
        
        # Set up initial state markers
        browser.execute_script("""
            localStorage.setItem('test_session_marker', 'phase1_test');
            localStorage.setItem('test_timestamp', Date.now().toString());
            sessionStorage.setItem('session_marker', 'active_session');
        """)
        
        # Verify markers were set
        session_marker = browser.execute_script("return localStorage.getItem('test_session_marker');")
        assert session_marker == 'phase1_test'
        
        print("PASS: Page navigation and state setup completed")
        
    except Exception as e:
        print(f"FAIL: Page navigation test failed: {e}")
        raise

def test_state_persistence(browser):
    """Test that browser state persists from previous test."""
    try:
        # Check if markers from previous test are still present
        session_marker = browser.execute_script("return localStorage.getItem('test_session_marker');")
        session_storage_marker = browser.execute_script("return sessionStorage.getItem('session_marker');")
        timestamp = browser.execute_script("return localStorage.getItem('test_timestamp');")
        
        # Verify state persistence
        assert session_marker == 'phase1_test', f"Expected 'phase1_test', got '{session_marker}'"
        assert session_storage_marker == 'active_session', f"Expected 'active_session', got '{session_storage_marker}'"
        assert timestamp is not None, "Timestamp should be preserved"
        
        # Add new state for next test
        browser.execute_script("""
            localStorage.setItem('persistence_test', 'completed');
            localStorage.setItem('test_counter', '1');
        """)
        
        print("PASS: State persistence verified successfully")
        
    except Exception as e:
        print(f"FAIL: State persistence test failed: {e}")
        raise

def test_dom_interaction(browser):
    """Test DOM interaction and element detection."""
    try:
        # Find basic page elements
        body_element = browser.find_element("tag name", "body")
        assert body_element is not None
        
        # Get page dimensions
        window_size = browser.get_window_size()
        print(f"Window size: {window_size}")
        
        # Test JavaScript execution
        page_ready = browser.execute_script("return document.readyState;")
        assert page_ready == 'complete'
        
        # Update test counter
        current_counter = browser.execute_script("return localStorage.getItem('test_counter') || '0';")
        new_counter = str(int(current_counter) + 1)
        browser.execute_script(f"localStorage.setItem('test_counter', '{new_counter}');")
        
        print(f"PASS: DOM interaction test completed (counter: {new_counter})")
        
    except Exception as e:
        print(f"FAIL: DOM interaction test failed: {e}")
        raise

def test_session_continuity_validation(browser):
    """Validate that session continuity is working correctly."""
    try:
        # Check all previous state markers
        markers = {
            'test_session_marker': browser.execute_script("return localStorage.getItem('test_session_marker');"),
            'persistence_test': browser.execute_script("return localStorage.getItem('persistence_test');"),
            'test_counter': browser.execute_script("return localStorage.getItem('test_counter');"),
            'session_marker': browser.execute_script("return sessionStorage.getItem('session_marker');")
        }
        
        # Verify all markers are present
        expected_values = {
            'test_session_marker': 'phase1_test',
            'persistence_test': 'completed',
            'test_counter': '2',  # Should be 2 after previous tests
            'session_marker': 'active_session'
        }
        
        for key, expected in expected_values.items():
            actual = markers[key]
            assert actual == expected, f"Marker '{key}': expected '{expected}', got '{actual}'"
        
        # Add final validation marker
        browser.execute_script("localStorage.setItem('continuity_validated', 'true');")
        
        print("PASS: Session continuity validation completed successfully")
        
    except Exception as e:
        print(f"FAIL: Session continuity validation failed: {e}")
        raise
'''
    
    # Create temporary test script file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_script_content)
        return f.name

def test_basic_session_workflow():
    """Test the complete basic session workflow."""
    print("\nTest 1: Basic Session Workflow")
    print("=" * 50)
    
    session_manager = BrowserSessionManager()
    
    try:
        # Step 1: Create session
        print("1.1 Creating browser session...")
        success = session_manager.create_session(headless=False)
        if not success:
            print("FAIL: Could not create browser session")
            return False
        print("PASS: Browser session created")
        
        # Step 2: Navigate to test page
        print("1.2 Navigating to test page...")
        test_url = "https://example.com"
        nav_success = session_manager.navigate_to(test_url)
        if not nav_success:
            print(f"FAIL: Could not navigate to {test_url}")
            return False
        print(f"PASS: Navigated to {test_url}")
        
        # Step 3: Execute test script with session preservation
        print("1.3 Executing test script with session preservation...")
        script_path = create_comprehensive_test_script()
        
        success, message = session_manager.execute_script_with_session(script_path)
        
        if success:
            print(f"PASS: Script executed successfully: {message}")
        else:
            print(f"FAIL: Script execution failed: {message}")
            return False
        
        # Step 4: Verify session is still active
        print("1.4 Verifying session state...")
        session_info = session_manager.get_session_info()
        if session_info["active"]:
            print("PASS: Session remained active after script execution")
        else:
            print("FAIL: Session became inactive after script execution")
            return False
        
        # Step 5: Verify state preservation
        print("1.5 Verifying state preservation...")
        continuity_marker = session_manager.driver.execute_script("return localStorage.getItem('continuity_validated');")
        if continuity_marker == 'true':
            print("PASS: State preservation verified")
        else:
            print(f"FAIL: State preservation failed: {continuity_marker}")
            return False
        
        # Clean up
        os.unlink(script_path)
        session_manager.cleanup_session()
        
        print("PASS: Basic session workflow completed successfully")
        return True
        
    except Exception as e:
        print(f"FAIL: Basic session workflow failed: {e}")
        session_manager.cleanup_session()
        return False

def test_multiple_script_executions():
    """Test multiple script executions with session preservation."""
    print("\nTest 2: Multiple Script Executions")
    print("=" * 50)
    
    session_manager = BrowserSessionManager()
    
    try:
        # Setup session
        print("2.1 Setting up session...")
        session_manager.create_session(headless=False)
        session_manager.navigate_to("https://example.com")
        print("PASS: Session setup completed")
        
        # Execute multiple scripts
        for i in range(3):
            print(f"2.{i+2} Executing script {i+1}...")
            script_path = create_comprehensive_test_script()
            
            success, message = session_manager.execute_script_with_session(script_path)
            
            if success:
                print(f"PASS: Script {i+1} executed successfully")
            else:
                print(f"FAIL: Script {i+1} execution failed: {message}")
                return False
            
            # Verify state accumulation
            counter = session_manager.driver.execute_script("return localStorage.getItem('test_counter');")
            expected_counter = str((i+1) * 4)  # 4 tests per script
            if counter == expected_counter:
                print(f"PASS: State accumulation verified (counter: {counter})")
            else:
                print(f"WARN: Unexpected counter value: {counter} (expected: {expected_counter})")
            
            os.unlink(script_path)
        
        # Final verification
        print("2.5 Final state verification...")
        final_marker = session_manager.driver.execute_script("return localStorage.getItem('continuity_validated');")
        if final_marker == 'true':
            print("PASS: Final state verification successful")
        else:
            print(f"FAIL: Final state verification failed: {final_marker}")
            return False
        
        session_manager.cleanup_session()
        print("PASS: Multiple script executions completed successfully")
        return True
        
    except Exception as e:
        print(f"FAIL: Multiple script executions failed: {e}")
        session_manager.cleanup_session()
        return False

def test_direct_executor():
    """Test the direct session executor specifically."""
    print("\nTest 3: Direct Session Executor")
    print("=" * 50)
    
    session_manager = BrowserSessionManager()
    
    try:
        # Setup session
        print("3.1 Setting up session for direct executor...")
        session_manager.create_session(headless=False)
        session_manager.navigate_to("https://example.com")
        
        # Create direct executor
        print("3.2 Creating direct session executor...")
        executor = DirectSessionExecutor(session_manager.driver, session_manager.session_id)
        
        # Execute script directly
        print("3.3 Executing script with direct executor...")
        script_path = create_comprehensive_test_script()
        
        success, message, results = executor.execute_script(script_path)
        
        if success:
            print(f"PASS: Direct execution successful: {message}")
            print(f"     Execution time: {results.get('execution_time', 0):.2f}s")
            print(f"     Tests executed: {len(results.get('test_results', []))}")
        else:
            print(f"FAIL: Direct execution failed: {message}")
            return False
        
        # Verify session info
        print("3.4 Verifying session info...")
        session_info = executor.get_session_info()
        if session_info["active"]:
            print(f"PASS: Session info verified: {session_info['current_url']}")
        else:
            print(f"FAIL: Session info invalid: {session_info}")
            return False
        
        os.unlink(script_path)
        session_manager.cleanup_session()
        
        print("PASS: Direct session executor test completed successfully")
        return True
        
    except Exception as e:
        print(f"FAIL: Direct session executor test failed: {e}")
        session_manager.cleanup_session()
        return False

def main():
    """Run complete session continuity tests."""
    print("Complete Session Continuity Tests - Phase 1")
    print("=" * 60)
    
    test_results = []
    
    try:
        # Test 1: Basic session workflow
        result1 = test_basic_session_workflow()
        test_results.append(("Basic Session Workflow", result1))
        
        # Test 2: Multiple script executions
        result2 = test_multiple_script_executions()
        test_results.append(("Multiple Script Executions", result2))
        
        # Test 3: Direct executor
        result3 = test_direct_executor()
        test_results.append(("Direct Session Executor", result3))
        
    except Exception as e:
        print(f"FAIL: Test suite failed with error: {e}")
        test_results.append(("Test Suite", False))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("Phase 1 Session Continuity Test Results:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<35} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("SUCCESS: Phase 1 session continuity implementation is working!")
        print("✓ Browser sessions persist through script execution")
        print("✓ DOM state, cookies, and storage are maintained")
        print("✓ Direct execution provides reliable session continuity")
        print("✓ Multiple script executions work with state accumulation")
        return True
    else:
        print("FAILURE: Some tests failed. Phase 1 needs additional work.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""
Basic Session Continuity Test for GretahAI ScriptWeaver

This test validates the core session continuity functionality implemented in Phase 1:
- Browser session creation and management
- Session-aware pytest execution
- State preservation during script execution
- Session handoff between ScriptWeaver and pytest

Test Scenarios:
1. Create browser session and navigate to test page
2. Execute a simple test script with session preservation
3. Validate that browser state is maintained
4. Test session cleanup and error handling
"""

import os
import sys
import tempfile
import time
import logging
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.browser_session_manager import BrowserSessionManager
from core.workflow_integration import WorkflowBrowserIntegration
from core.session_pytest_runner import SessionPytestRunner
from state_manager import StateManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("SessionContinuityTest")

def create_test_script() -> str:
    """Create a simple test script for session continuity testing."""
    test_script_content = '''
import pytest
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

def test_basic_navigation(browser):
    """Test basic navigation and page interaction."""
    try:
        # Get current URL
        current_url = browser.current_url
        print(f"Current URL: {current_url}")
        
        # Verify page title
        title = browser.title
        print(f"Page title: {title}")
        
        # Try to find some basic elements
        body_element = browser.find_element(By.TAG_NAME, "body")
        assert body_element is not None
        
        # Add a marker to local storage to test state preservation
        browser.execute_script("localStorage.setItem('session_test_marker', 'session_continuity_test');")
        
        # Verify the marker was set
        marker = browser.execute_script("return localStorage.getItem('session_test_marker');")
        assert marker == 'session_continuity_test'
        
        print("PASS: Basic navigation test completed successfully")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise

def test_state_preservation(browser):
    """Test that browser state is preserved from previous execution."""
    try:
        # Check if our marker from previous test is still there
        marker = browser.execute_script("return localStorage.getItem('session_test_marker');")
        
        if marker == 'session_continuity_test':
            print("PASS: Session state preserved - marker found in localStorage")
        else:
            print("WARN: Session state not preserved - marker not found")

        # Add another marker for next test
        browser.execute_script("localStorage.setItem('second_test_marker', 'second_test_value');")

        print("PASS: State preservation test completed")
        
    except Exception as e:
        print(f"❌ State preservation test failed: {e}")
        raise
'''
    
    # Create temporary test script file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_script_content)
        return f.name

def test_basic_session_creation():
    """Test basic browser session creation and management."""
    print("\n🧪 Test 1: Basic Session Creation")
    
    session_manager = BrowserSessionManager()
    
    try:
        # Create session
        success = session_manager.create_session(headless=False)
        assert success, "Failed to create browser session"
        print("PASS: Browser session created successfully")

        # Navigate to test page
        test_url = "https://example.com"
        nav_success = session_manager.navigate_to(test_url)
        assert nav_success, f"Failed to navigate to {test_url}"
        print(f"PASS: Successfully navigated to {test_url}")

        # Get session info
        session_info = session_manager.get_session_info()
        assert session_info["active"], "Session should be active"
        print(f"PASS: Session info retrieved: {session_info['session_id'][:8]}...")
        
        return session_manager
        
    except Exception as e:
        print(f"❌ Basic session creation test failed: {e}")
        session_manager.cleanup_session()
        raise

def test_session_pytest_execution(session_manager):
    """Test executing pytest with session preservation."""
    print("\n🧪 Test 2: Session-Aware Pytest Execution")
    
    try:
        # Create test script
        script_path = create_test_script()
        print(f"✅ Created test script: {script_path}")
        
        # Execute script with session preservation
        success, message = session_manager.execute_script_with_session(script_path)
        
        if success:
            print(f"✅ Script executed successfully: {message}")
        else:
            print(f"❌ Script execution failed: {message}")
            return False
        
        # Verify session is still active
        session_info = session_manager.get_session_info()
        if session_info["active"]:
            print("✅ Session remained active after script execution")
        else:
            print("❌ Session became inactive after script execution")
            return False
        
        # Clean up test script
        os.unlink(script_path)
        
        return True
        
    except Exception as e:
        print(f"❌ Session pytest execution test failed: {e}")
        return False

def test_workflow_integration():
    """Test the workflow integration with session continuity."""
    print("\n🧪 Test 3: Workflow Integration")
    
    integration = WorkflowBrowserIntegration()
    
    try:
        # Create mock state manager
        class MockState:
            def __init__(self):
                self.website_url = "https://example.com"
                self.browser_session_active = False
                self.session_ready_for_element_selection = False
            
            def set_session_ready_for_element_selection(self, ready):
                self.session_ready_for_element_selection = ready
        
        state = MockState()
        
        # Initialize session
        success = integration.initialize_session(state, headless=False)
        assert success, "Failed to initialize workflow session"
        print("✅ Workflow session initialized")
        
        # Check session status
        assert integration.is_session_active(), "Session should be active"
        print("✅ Session is active")
        
        # Create and execute test script
        script_path = create_test_script()
        success, message, results = integration.execute_script_with_continuity(state, script_path)
        
        if success:
            print(f"✅ Workflow script execution successful: {message}")
            print(f"   Session preserved: {results.get('session_preserved', False)}")
            print(f"   Ready for element selection: {results.get('ready_for_element_selection', False)}")
        else:
            print(f"❌ Workflow script execution failed: {message}")
            return False
        
        # Test transition preparation
        transition_success = integration.transition_to_element_selection(state)
        if transition_success:
            print("✅ Transition to element selection prepared successfully")
        else:
            print("❌ Failed to prepare transition to element selection")
            return False
        
        # Clean up
        os.unlink(script_path)
        integration.cleanup_session()
        print("✅ Workflow integration test completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow integration test failed: {e}")
        integration.cleanup_session()
        return False

def test_session_state_preservation():
    """Test that browser state is actually preserved across executions."""
    print("\n🧪 Test 4: Session State Preservation")
    
    session_manager = BrowserSessionManager()
    
    try:
        # Create session and navigate
        session_manager.create_session(headless=False)
        session_manager.navigate_to("https://example.com")
        
        # Set initial state
        session_manager.driver.execute_script("""
            localStorage.setItem('test_key', 'test_value');
            sessionStorage.setItem('session_key', 'session_value');
        """)
        
        # Execute first script
        script1_path = create_test_script()
        success1, message1 = session_manager.execute_script_with_session(script1_path)
        assert success1, f"First script execution failed: {message1}"
        print("✅ First script executed successfully")
        
        # Execute second script to check state preservation
        script2_content = '''
def test_preserved_state(browser):
    """Test that state from previous execution is preserved."""
    # Check localStorage
    test_value = browser.execute_script("return localStorage.getItem('test_key');")
    assert test_value == 'test_value', f"Expected 'test_value', got '{test_value}'"
    
    # Check sessionStorage
    session_value = browser.execute_script("return sessionStorage.getItem('session_key');")
    assert session_value == 'session_value', f"Expected 'session_value', got '{session_value}'"
    
    # Check marker from first script
    marker = browser.execute_script("return localStorage.getItem('session_test_marker');")
    assert marker == 'session_continuity_test', f"Expected marker not found: '{marker}'"
    
    print("✅ All state preservation checks passed")
'''
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(script2_content)
            script2_path = f.name
        
        success2, message2 = session_manager.execute_script_with_session(script2_path)
        
        if success2:
            print("✅ State preservation verified successfully")
        else:
            print(f"❌ State preservation test failed: {message2}")
            return False
        
        # Clean up
        os.unlink(script1_path)
        os.unlink(script2_path)
        session_manager.cleanup_session()
        
        return True
        
    except Exception as e:
        print(f"❌ Session state preservation test failed: {e}")
        session_manager.cleanup_session()
        return False

def main():
    """Run all session continuity tests."""
    print("🚀 Starting Basic Session Continuity Tests")
    print("=" * 60)
    
    test_results = []
    
    try:
        # Test 1: Basic session creation
        session_manager = test_basic_session_creation()
        test_results.append(("Basic Session Creation", True))
        
        # Test 2: Session-aware pytest execution
        result2 = test_session_pytest_execution(session_manager)
        test_results.append(("Session Pytest Execution", result2))
        
        # Clean up from test 2
        session_manager.cleanup_session()
        
        # Test 3: Workflow integration
        result3 = test_workflow_integration()
        test_results.append(("Workflow Integration", result3))
        
        # Test 4: State preservation
        result4 = test_session_state_preservation()
        test_results.append(("Session State Preservation", result4))
        
    except Exception as e:
        print(f"❌ Test suite failed with error: {e}")
        test_results.append(("Test Suite", False))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Session continuity is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

"""
Workflow Integration for Browser State Continuity

This module provides integration points for the browser session manager
with the existing GretahAI ScriptWeaver workflow, enabling seamless
browser state continuity across Stage 6→7→4 transitions.

Key Integration Points:
- Stage 4: Enhanced element selection with session continuity
- Stage 6: Script generation with session awareness
- Stage 7: Script execution with session preservation
- State management integration
- Error handling and recovery
"""

import os
import logging
import streamlit as st
from typing import Optional, Dict, Any, Tuple
from datetime import datetime

from core.browser_session_manager import BrowserSessionManager
from core.interactive_selector import inject_element_selection_script, get_selected_element_info
from state_manager import StateManager, StateStage

logger = logging.getLogger("ScriptWeaver.workflow_integration")

class WorkflowBrowserIntegration:
    """
    Integrates browser session management with the existing workflow.
    
    This class provides the bridge between the persistent browser session
    manager and the existing stage-based workflow architecture.
    """
    
    def __init__(self):
        self.session_manager: Optional[BrowserSessionManager] = None
        self.integration_active: bool = False
        
    def initialize_session(self, state: StateManager, headless: bool = False) -> bool:
        """
        Initialize a browser session for workflow continuity.
        
        Args:
            state: StateManager instance
            headless: Whether to run browser in headless mode
            
        Returns:
            bool: True if initialization successful
        """
        try:
            # Clean up existing session if any
            if self.session_manager:
                self.session_manager.cleanup_session()
            
            # Create new session manager
            self.session_manager = BrowserSessionManager()
            
            # Create browser session
            if self.session_manager.create_session(headless=headless):
                self.integration_active = True
                
                # Navigate to website URL if available
                if hasattr(state, 'website_url') and state.website_url:
                    if self.session_manager.navigate_to(state.website_url):
                        logger.info(f"Session initialized and navigated to {state.website_url}")
                    else:
                        logger.warning(f"Session created but navigation to {state.website_url} failed")
                
                return True
            else:
                logger.error("Failed to create browser session")
                return False
                
        except Exception as e:
            logger.error(f"Error initializing browser session: {e}")
            return False
    
    def is_session_active(self) -> bool:
        """Check if browser session is active and ready."""
        return (self.integration_active and 
                self.session_manager is not None and 
                self.session_manager.is_active)
    
    def get_session_info(self) -> Dict[str, Any]:
        """Get current session information for UI display."""
        if not self.session_manager:
            return {"active": False, "error": "No session manager"}
        
        return self.session_manager.get_session_info()
    
    def enhanced_element_selection(self, state: StateManager, 
                                 selected_step_table_entry: Dict[str, Any],
                                 selected_original_step: Dict[str, Any]) -> Tuple[bool, Optional[Dict], str]:
        """
        Enhanced element selection using persistent browser session.
        
        This method provides element selection with preserved browser state,
        eliminating the need to replay steps or recreate browser context.
        
        Args:
            state: StateManager instance
            selected_step_table_entry: Current step table entry
            selected_original_step: Original step data
            
        Returns:
            Tuple[bool, Optional[Dict], str]: (success, element_data, message)
        """
        if not self.is_session_active():
            return False, None, "Browser session not active"
        
        try:
            # Prepare session for element selection
            if not self.session_manager.prepare_for_element_selection():
                return False, None, "Failed to prepare browser session for element selection"
            
            # Inject element selection script
            if not inject_element_selection_script(self.session_manager.driver):
                return False, None, "Failed to inject element selection script"
            
            logger.info("Enhanced element selector launched with preserved browser state")
            
            # Wait for element selection with timeout
            import time
            max_wait_time = 60  # seconds
            poll_interval = 0.5
            start_time = time.time()
            
            while time.time() - start_time < max_wait_time:
                selected_element_info = get_selected_element_info(self.session_manager.driver)
                if selected_element_info:
                    logger.info("Element selected successfully with preserved state")
                    return True, selected_element_info, "Element selected successfully"
                
                time.sleep(poll_interval)
            
            return False, None, "Element selection timed out"
            
        except Exception as e:
            logger.error(f"Error in enhanced element selection: {e}")
            return False, None, f"Error in element selection: {e}"
    
    def execute_script_with_continuity(self, state: StateManager, 
                                     script_path: str) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Execute script while preserving browser session for continuity.
        
        This method executes the generated test script while maintaining
        the browser session for subsequent element selection.
        
        Args:
            state: StateManager instance
            script_path: Path to the script to execute
            
        Returns:
            Tuple[bool, str, Dict]: (success, message, execution_results)
        """
        if not self.is_session_active():
            return False, "Browser session not active", {}
        
        try:
            # Validate script file exists
            if not os.path.exists(script_path):
                return False, f"Script file not found: {script_path}", {}

            # Execute script with session preservation using the enhanced method
            success, message = self.session_manager.execute_script_with_session(script_path)

            if success:
                # Prepare for next stage
                preparation_success = self.session_manager.prepare_for_element_selection()

                execution_results = {
                    "execution_time": datetime.now().isoformat(),
                    "script_path": script_path,
                    "session_preserved": True,
                    "ready_for_element_selection": preparation_success,
                    "session_id": self.session_manager.session_id,
                    "current_url": self.session_manager.current_url
                }

                # Update state manager with session status
                if hasattr(state, 'set_session_ready_for_element_selection'):
                    state.set_session_ready_for_element_selection(preparation_success)

                logger.info("Script executed successfully with session continuity")
                return True, message, execution_results
            else:
                logger.error(f"Script execution failed: {message}")
                return False, message, {"session_preserved": False}

        except Exception as e:
            logger.error(f"Error executing script with continuity: {e}")
            return False, f"Execution error: {e}", {"session_preserved": False}
    
    def transition_to_element_selection(self, state: StateManager) -> bool:
        """
        Prepare for transition from Stage 7 to Stage 4 with preserved state.
        
        Args:
            state: StateManager instance
            
        Returns:
            bool: True if transition preparation successful
        """
        if not self.is_session_active():
            logger.warning("Cannot transition - browser session not active")
            return False
        
        try:
            # Verify session is ready for element selection
            if not self.session_manager.prepare_for_element_selection():
                logger.error("Failed to prepare session for element selection transition")
                return False
            
            # Update state to indicate session continuity is available
            if not hasattr(state, 'browser_session_active'):
                state.browser_session_active = True
            
            if not hasattr(state, 'session_ready_for_element_selection'):
                state.session_ready_for_element_selection = True
            
            logger.info("Successfully prepared for Stage 7→4 transition with preserved state")
            return True
            
        except Exception as e:
            logger.error(f"Error preparing for element selection transition: {e}")
            return False
    
    def cleanup_session(self) -> None:
        """Clean up browser session and reset integration state."""
        try:
            if self.session_manager:
                self.session_manager.cleanup_session()
                self.session_manager = None
            
            self.integration_active = False
            logger.info("Workflow browser integration cleaned up")
            
        except Exception as e:
            logger.error(f"Error during integration cleanup: {e}")
    
    def extend_session(self, additional_time: int = 1800) -> bool:
        """
        Extend the browser session timeout.
        
        Args:
            additional_time: Additional time in seconds
            
        Returns:
            bool: True if extension successful
        """
        if not self.session_manager:
            return False
        
        return self.session_manager.extend_session(additional_time)
    
    def handle_session_error(self, error: Exception) -> Dict[str, Any]:
        """
        Handle session errors and provide recovery options.
        
        Args:
            error: The error that occurred
            
        Returns:
            Dict containing error info and recovery options
        """
        logger.error(f"Browser session error: {error}")
        
        # Mark session as inactive
        self.integration_active = False
        
        recovery_options = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "recovery_actions": [
                "Restart browser session",
                "Continue without session continuity",
                "Return to previous stage"
            ],
            "session_recoverable": self.session_manager is not None
        }
        
        return recovery_options

# Global instance for workflow integration
workflow_browser_integration = WorkflowBrowserIntegration()

def get_workflow_browser_integration() -> WorkflowBrowserIntegration:
    """Get the global workflow browser integration instance."""
    return workflow_browser_integration

def initialize_browser_continuity(state: StateManager, headless: bool = False) -> bool:
    """
    Initialize browser continuity for the workflow.
    
    Args:
        state: StateManager instance
        headless: Whether to run browser in headless mode
        
    Returns:
        bool: True if initialization successful
    """
    integration = get_workflow_browser_integration()
    return integration.initialize_session(state, headless)

def cleanup_browser_continuity() -> None:
    """Clean up browser continuity resources."""
    integration = get_workflow_browser_integration()
    integration.cleanup_session()

def is_browser_continuity_active() -> bool:
    """Check if browser continuity is active."""
    integration = get_workflow_browser_integration()
    return integration.is_session_active()

def get_browser_continuity_info() -> Dict[str, Any]:
    """Get browser continuity session information."""
    integration = get_workflow_browser_integration()
    return integration.get_session_info()

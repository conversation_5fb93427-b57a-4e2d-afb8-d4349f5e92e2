"""
Session-Aware Script Generator for GretahAI ScriptWeaver Phase 2

This module enhances the script generation pipeline to produce session-aware
test scripts by default, enabling seamless browser session continuity throughout
the complete workflow cycle (Stages 4-7).

Key Features:
- Native session continuity support in generated scripts
- Conditional logic for existing browser sessions vs. new browser creation
- Session state validation and recovery in generated scripts
- Backward compatibility with existing script generation
- Enhanced browser fixture integration
- Automatic session handoff mechanisms
"""

import os
import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

logger = logging.getLogger("ScriptWeaver.session_aware_script_generator")

class SessionAwareScriptGenerator:
    """
    Generates test scripts with native session continuity support.
    
    This class enhances the existing script generation pipeline to produce
    scripts that can seamlessly work with preserved browser sessions while
    maintaining backward compatibility.
    """
    
    def __init__(self):
        self.session_continuity_enabled = True
        self.backward_compatibility = True
        
    def generate_session_aware_script(self, 
                                    original_script: str,
                                    test_case: Dict[str, Any],
                                    enable_continuity: bool = True) -> Tuple[str, Dict[str, Any]]:
        """
        Generate a session-aware version of the test script.
        
        Args:
            original_script: The original generated script content
            test_case: Test case data for context
            enable_continuity: Whether to enable session continuity features
            
        Returns:
            Tuple[str, Dict]: (enhanced_script, metadata)
        """
        try:
            if not enable_continuity:
                return original_script, {"session_aware": False}
            
            # Generate session-aware script with enhanced browser fixture
            enhanced_script = self._inject_session_awareness(original_script, test_case)
            
            metadata = {
                "session_aware": True,
                "backward_compatible": True,
                "generation_timestamp": datetime.now().isoformat(),
                "test_case_id": test_case.get("Test Case ID", "unknown"),
                "continuity_features": [
                    "session_preservation",
                    "state_validation",
                    "automatic_fallback",
                    "browser_fixture_enhancement"
                ]
            }
            
            logger.info(f"Generated session-aware script for test case: {test_case.get('Test Case ID', 'unknown')}")
            return enhanced_script, metadata
            
        except Exception as e:
            logger.error(f"Error generating session-aware script: {e}")
            # Return original script as fallback
            return original_script, {"session_aware": False, "error": str(e)}
    
    def _inject_session_awareness(self, original_script: str, test_case: Dict[str, Any]) -> str:
        """
        Inject session awareness into the original script.
        
        Args:
            original_script: Original script content
            test_case: Test case data for context
            
        Returns:
            str: Enhanced script with session awareness
        """
        # Extract test case information
        test_case_id = test_case.get("Test Case ID", "unknown")
        test_case_name = test_case.get("Test Case Name", "Unknown Test")
        
        # Create session-aware header
        session_header = self._generate_session_aware_header(test_case_id, test_case_name)
        
        # Create enhanced browser fixture
        enhanced_fixture = self._generate_enhanced_browser_fixture()
        
        # Process original script to work with enhanced fixture
        processed_script = self._process_original_script(original_script)
        
        # Combine all components
        enhanced_script = f"{session_header}\n\n{enhanced_fixture}\n\n{processed_script}"
        
        return enhanced_script
    
    def _generate_session_aware_header(self, test_case_id: str, test_case_name: str) -> str:
        """Generate session-aware script header."""
        return f'''"""
Session-Aware Test Script for GretahAI ScriptWeaver
Generated with Phase 2 Browser Session Continuity Support

Test Case ID: {test_case_id}
Test Case Name: {test_case_name}
Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

This script supports:
- Browser session continuity across workflow stages
- Automatic session preservation and restoration
- Fallback to standard browser creation when needed
- State validation and recovery mechanisms
"""

import pytest
import os
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Configure logging for session continuity
logger = logging.getLogger("SessionAwareTest")'''
    
    def _generate_enhanced_browser_fixture(self) -> str:
        """Generate enhanced browser fixture with session continuity support."""
        return '''
@pytest.fixture(scope="module")
def browser():
    """
    Enhanced browser fixture with session continuity support.
    
    This fixture automatically detects and uses existing browser sessions
    from GretahAI ScriptWeaver workflow when available, falling back to
    standard browser creation when needed.
    """
    driver = None
    session_preserved = False
    
    try:
        # Check for ScriptWeaver session continuity mode
        session_mode = os.environ.get('SCRIPTWEAVER_SESSION_MODE', '0') == '1'
        
        if session_mode:
            logger.info("ScriptWeaver session continuity mode detected")
            
            # Try to get shared driver from ScriptWeaver
            try:
                # Import session continuity functions
                from core.session_pytest_runner import get_shared_driver
                shared_driver = get_shared_driver()
                
                if shared_driver:
                    logger.info("Using shared browser session from ScriptWeaver")
                    session_preserved = True
                    yield shared_driver
                    return
                    
            except ImportError:
                logger.warning("Session continuity functions not available")
            except Exception as e:
                logger.warning(f"Could not access shared driver: {e}")
        
        # Fallback to standard browser creation
        logger.info("Creating new browser instance")
        chrome_options = Options()
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-popup-blocking")
        
        # Check for headless mode
        if os.environ.get('HEADLESS') == '1':
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--window-size=1920,1080')
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.implicitly_wait(10)
        
        logger.info("Browser instance created successfully")
        yield driver
        
    except Exception as e:
        logger.error(f"Error in browser fixture: {e}")
        if not driver:
            # Emergency fallback
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            driver = webdriver.Chrome(options=chrome_options)
        yield driver
        
    finally:
        # Only quit if we created the driver (not shared from ScriptWeaver)
        if driver and not session_preserved:
            try:
                driver.quit()
                logger.info("Browser instance closed")
            except Exception as e:
                logger.error(f"Error closing browser: {e}")


def validate_session_state(browser):
    """
    Validate that the browser session is in a good state.
    
    Args:
        browser: WebDriver instance
        
    Returns:
        bool: True if session is valid
    """
    try:
        # Basic responsiveness check
        current_url = browser.current_url
        ready_state = browser.execute_script("return document.readyState;")
        
        logger.info(f"Session validation - URL: {current_url}, Ready state: {ready_state}")
        return ready_state == 'complete'
        
    except Exception as e:
        logger.error(f"Session validation failed: {e}")
        return False


def preserve_test_state(browser, state_key: str, state_value: str):
    """
    Preserve test state in browser storage for continuity.
    
    Args:
        browser: WebDriver instance
        state_key: Key for the state data
        state_value: Value to preserve
    """
    try:
        browser.execute_script(
            "localStorage.setItem(arguments[0], arguments[1]);",
            f"scriptweaver_test_{state_key}",
            state_value
        )
        logger.info(f"Preserved test state: {state_key}")
    except Exception as e:
        logger.warning(f"Could not preserve test state {state_key}: {e}")


def get_preserved_state(browser, state_key: str) -> str:
    """
    Retrieve preserved test state from browser storage.
    
    Args:
        browser: WebDriver instance
        state_key: Key for the state data
        
    Returns:
        str: Retrieved state value or None
    """
    try:
        value = browser.execute_script(
            "return localStorage.getItem(arguments[0]);",
            f"scriptweaver_test_{state_key}"
        )
        if value:
            logger.info(f"Retrieved preserved state: {state_key}")
        return value
    except Exception as e:
        logger.warning(f"Could not retrieve preserved state {state_key}: {e}")
        return None'''
    
    def _process_original_script(self, original_script: str) -> str:
        """
        Process the original script to work with enhanced session awareness.
        
        Args:
            original_script: Original script content
            
        Returns:
            str: Processed script content
        """
        # Remove any existing browser fixture definitions to avoid conflicts
        lines = original_script.split('\n')
        processed_lines = []
        skip_fixture = False
        
        for line in lines:
            # Skip existing browser fixture definitions
            if '@pytest.fixture' in line and 'browser' in line:
                skip_fixture = True
                continue
            elif skip_fixture and (line.strip() == '' or line.startswith('def ') or line.startswith('@')):
                skip_fixture = False
            
            if not skip_fixture:
                processed_lines.append(line)
        
        processed_script = '\n'.join(processed_lines)
        
        # Add session state validation to test functions
        processed_script = self._add_session_validation(processed_script)
        
        return processed_script
    
    def _add_session_validation(self, script_content: str) -> str:
        """
        Add session validation to test functions.
        
        Args:
            script_content: Script content to enhance
            
        Returns:
            str: Enhanced script with session validation
        """
        lines = script_content.split('\n')
        enhanced_lines = []
        
        for line in lines:
            enhanced_lines.append(line)
            
            # Add session validation at the start of test functions
            if line.strip().startswith('def test_') and '(browser' in line:
                # Add session validation after function definition
                enhanced_lines.extend([
                    '    """Enhanced test function with session continuity support."""',
                    '    # Validate session state before proceeding',
                    '    if not validate_session_state(browser):',
                    '        logger.warning("Session validation failed, but continuing with test")',
                    '    ',
                    '    # Preserve test start state',
                    '    preserve_test_state(browser, "test_start", "true")',
                    '    ',
                    '    try:'
                ])
                
                # We'll need to indent the rest of the function
                in_test_function = True
                continue
        
        return '\n'.join(enhanced_lines)


def enhance_existing_script_generation(original_generator_func):
    """
    Decorator to enhance existing script generation functions with session awareness.
    
    Args:
        original_generator_func: Original script generation function
        
    Returns:
        Enhanced function with session awareness
    """
    def enhanced_generator(*args, **kwargs):
        # Call original generator
        result = original_generator_func(*args, **kwargs)
        
        # Check if session awareness should be applied
        enable_continuity = kwargs.get('enable_session_continuity', True)
        
        if enable_continuity and isinstance(result, tuple) and len(result) >= 2:
            # Assume result is (merged_script, step_specific_script, ...)
            merged_script, step_specific_script = result[0], result[1]
            
            # Create session-aware generator
            generator = SessionAwareScriptGenerator()
            
            # Enhance both scripts
            if merged_script:
                enhanced_merged, _ = generator.generate_session_aware_script(
                    merged_script, 
                    kwargs.get('test_case', {}),
                    enable_continuity
                )
            else:
                enhanced_merged = merged_script
            
            if step_specific_script:
                enhanced_step, _ = generator.generate_session_aware_script(
                    step_specific_script,
                    kwargs.get('test_case', {}),
                    enable_continuity
                )
            else:
                enhanced_step = step_specific_script
            
            # Return enhanced result
            if len(result) == 2:
                return (enhanced_merged, enhanced_step)
            else:
                return (enhanced_merged, enhanced_step) + result[2:]
        
        return result
    
    return enhanced_generator

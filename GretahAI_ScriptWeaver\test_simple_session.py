"""
Simple Session Continuity Test

This test validates the core session continuity functionality with minimal complexity.
"""

import os
import sys
import tempfile
import logging

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.browser_session_manager import BrowserSessionManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("SimpleSessionTest")

def create_simple_test_script() -> str:
    """Create a very simple test script for session continuity testing."""
    test_script_content = '''
import pytest

def test_simple_navigation(browser):
    """Test basic navigation."""
    try:
        # Get current URL
        current_url = browser.current_url
        print(f"Current URL: {current_url}")
        
        # Verify page title
        title = browser.title
        print(f"Page title: {title}")
        
        # Add a marker to local storage
        browser.execute_script("localStorage.setItem('test_marker', 'session_test');")
        
        # Verify the marker was set
        marker = browser.execute_script("return localStorage.getItem('test_marker');")
        assert marker == 'session_test'
        
        print("PASS: Simple navigation test completed")
        
    except Exception as e:
        print(f"FAIL: Test failed: {e}")
        raise
'''
    
    # Create temporary test script file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_script_content)
        return f.name

def test_basic_session_execution():
    """Test basic session creation and script execution."""
    print("\nTest: Basic Session Execution")
    print("=" * 50)
    
    session_manager = BrowserSessionManager()
    
    try:
        # Create session
        print("1. Creating browser session...")
        success = session_manager.create_session(headless=False)
        if not success:
            print("FAIL: Could not create browser session")
            return False
        print("PASS: Browser session created")
        
        # Navigate to test page
        print("2. Navigating to test page...")
        test_url = "https://example.com"
        nav_success = session_manager.navigate_to(test_url)
        if not nav_success:
            print(f"FAIL: Could not navigate to {test_url}")
            return False
        print(f"PASS: Navigated to {test_url}")
        
        # Create and execute test script
        print("3. Creating test script...")
        script_path = create_simple_test_script()
        print(f"PASS: Created test script: {script_path}")
        
        print("4. Executing script with session preservation...")
        success, message = session_manager.execute_script_with_session(script_path)
        
        if success:
            print(f"PASS: Script executed successfully: {message}")
        else:
            print(f"FAIL: Script execution failed: {message}")
            return False
        
        # Verify session is still active
        print("5. Verifying session state...")
        session_info = session_manager.get_session_info()
        if session_info["active"]:
            print("PASS: Session remained active after script execution")
        else:
            print("FAIL: Session became inactive after script execution")
            return False
        
        # Clean up
        os.unlink(script_path)
        session_manager.cleanup_session()
        
        print("PASS: All tests completed successfully")
        return True
        
    except Exception as e:
        print(f"FAIL: Test failed with error: {e}")
        session_manager.cleanup_session()
        return False

def test_session_state_preservation():
    """Test that browser state is preserved across script executions."""
    print("\nTest: Session State Preservation")
    print("=" * 50)
    
    session_manager = BrowserSessionManager()
    
    try:
        # Create session and navigate
        print("1. Setting up browser session...")
        session_manager.create_session(headless=False)
        session_manager.navigate_to("https://example.com")
        
        # Set initial state
        print("2. Setting initial browser state...")
        session_manager.driver.execute_script("""
            localStorage.setItem('initial_key', 'initial_value');
            sessionStorage.setItem('session_key', 'session_value');
        """)
        print("PASS: Initial state set")
        
        # Execute first script
        print("3. Executing first script...")
        script1_path = create_simple_test_script()
        success1, message1 = session_manager.execute_script_with_session(script1_path)
        if not success1:
            print(f"FAIL: First script execution failed: {message1}")
            return False
        print("PASS: First script executed")
        
        # Check if state is preserved
        print("4. Checking state preservation...")
        initial_value = session_manager.driver.execute_script("return localStorage.getItem('initial_key');")
        test_marker = session_manager.driver.execute_script("return localStorage.getItem('test_marker');")
        session_value = session_manager.driver.execute_script("return sessionStorage.getItem('session_key');")
        
        if initial_value == 'initial_value':
            print("PASS: Initial localStorage preserved")
        else:
            print(f"FAIL: Initial localStorage not preserved: {initial_value}")
            return False
        
        if test_marker == 'session_test':
            print("PASS: Test marker from script preserved")
        else:
            print(f"FAIL: Test marker not preserved: {test_marker}")
            return False
        
        if session_value == 'session_value':
            print("PASS: SessionStorage preserved")
        else:
            print(f"FAIL: SessionStorage not preserved: {session_value}")
            return False
        
        # Clean up
        os.unlink(script1_path)
        session_manager.cleanup_session()
        
        print("PASS: State preservation test completed successfully")
        return True
        
    except Exception as e:
        print(f"FAIL: State preservation test failed: {e}")
        session_manager.cleanup_session()
        return False

def main():
    """Run simple session continuity tests."""
    print("Simple Session Continuity Tests")
    print("=" * 60)
    
    test_results = []
    
    try:
        # Test 1: Basic session execution
        result1 = test_basic_session_execution()
        test_results.append(("Basic Session Execution", result1))
        
        # Test 2: State preservation
        result2 = test_session_state_preservation()
        test_results.append(("Session State Preservation", result2))
        
    except Exception as e:
        print(f"FAIL: Test suite failed with error: {e}")
        test_results.append(("Test Suite", False))
    
    # Print results summary
    print("\n" + "=" * 60)
    print("Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("SUCCESS: All tests passed! Session continuity is working.")
        return True
    else:
        print("FAILURE: Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
